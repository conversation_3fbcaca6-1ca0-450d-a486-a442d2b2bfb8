{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Transactions=()=>{const{t}=useTranslation();const[transactions,setTransactions]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchTransactions=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch transactions with user information\nconst{data,error}=await supabase.from('transactions').select(`\n                    id,\n                    tx_date,\n                    sender_user_id,\n                    receiver_user_id,\n                    amount_net,\n                    tx_type,\n                    filecoin_msg_id,\n                    agent_id,\n                    created_at,\n                    sender:users!sender_user_id (\n                        email\n                    ),\n                    receiver:users!receiver_user_id (\n                        email\n                    ),\n                    agent:agent_profiles!agent_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    )\n                `).order('tx_date',{ascending:false});if(error){console.error('Error fetching transactions:',error);}else{setTransactions(data);}setLoading(false);};fetchTransactions();},[]);const getTransactionTypeColor=txType=>{switch(txType){case'deposit':return'success';case'withdrawal':return'danger';case'transfer':return'primary';case'reward':return'info';default:return'secondary';}};if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_transactions')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('transactions')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('transaction_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('tx_date')}),/*#__PURE__*/_jsx(\"th\",{children:t('sender')}),/*#__PURE__*/_jsx(\"th\",{children:t('receiver')}),/*#__PURE__*/_jsx(\"th\",{children:t('amount')}),/*#__PURE__*/_jsx(\"th\",{children:t('tx_type')}),/*#__PURE__*/_jsx(\"th\",{children:t('filecoin_msg_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('agent')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:transactions.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"9\",className:\"text-center\",children:t('no_transactions_available')})}):transactions.map(transaction=>{var _transaction$sender,_transaction$receiver,_transaction$agent,_transaction$agent$us;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsxs(\"td\",{children:[transaction.id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{children:new Date(transaction.tx_date).toLocaleString()}),/*#__PURE__*/_jsx(\"td\",{children:((_transaction$sender=transaction.sender)===null||_transaction$sender===void 0?void 0:_transaction$sender.email)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_transaction$receiver=transaction.receiver)===null||_transaction$receiver===void 0?void 0:_transaction$receiver.email)||'-'}),/*#__PURE__*/_jsxs(\"td\",{children:[transaction.amount_net?Number(transaction.amount_net).toFixed(6):'0',\" FIL\"]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:getTransactionTypeColor(transaction.tx_type),children:transaction.tx_type||'unknown'})}),/*#__PURE__*/_jsx(\"td\",{children:transaction.filecoin_msg_id?/*#__PURE__*/_jsxs(\"span\",{title:transaction.filecoin_msg_id,children:[transaction.filecoin_msg_id.substring(0,10),\"...\"]}):'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_transaction$agent=transaction.agent)===null||_transaction$agent===void 0?void 0:(_transaction$agent$us=_transaction$agent.users)===null||_transaction$agent$us===void 0?void 0:_transaction$agent$us.email)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:new Date(transaction.created_at).toLocaleString()})]},transaction.id);})})]})})})})})]});};export default Transactions;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Transactions", "t", "transactions", "setTransactions", "loading", "setLoading", "fetchTransactions", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "getTransactionTypeColor", "txType", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "transaction", "_transaction$sender", "_transaction$receiver", "_transaction$agent", "_transaction$agent$us", "id", "substring", "Date", "tx_date", "toLocaleString", "sender", "email", "receiver", "amount_net", "Number", "toFixed", "bg", "tx_type", "filecoin_msg_id", "title", "agent", "users", "created_at"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/Transactions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst Transactions = () => {\n    const { t } = useTranslation();\n    const [transactions, setTransactions] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchTransactions = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch transactions with user information\n            const { data, error } = await supabase\n                .from('transactions')\n                .select(`\n                    id,\n                    tx_date,\n                    sender_user_id,\n                    receiver_user_id,\n                    amount_net,\n                    tx_type,\n                    filecoin_msg_id,\n                    agent_id,\n                    created_at,\n                    sender:users!sender_user_id (\n                        email\n                    ),\n                    receiver:users!receiver_user_id (\n                        email\n                    ),\n                    agent:agent_profiles!agent_id (\n                        user_id,\n                        users (\n                            email\n                        )\n                    )\n                `)\n                .order('tx_date', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching transactions:', error);\n            } else {\n                setTransactions(data);\n            }\n            setLoading(false);\n        };\n\n        fetchTransactions();\n    }, []);\n\n    const getTransactionTypeColor = (txType) => {\n        switch (txType) {\n            case 'deposit':\n                return 'success';\n            case 'withdrawal':\n                return 'danger';\n            case 'transfer':\n                return 'primary';\n            case 'reward':\n                return 'info';\n            default:\n                return 'secondary';\n        }\n    };\n\n    if (loading) {\n        return <div>{t('loading_transactions')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('transactions')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('transaction_id')}</th>\n                                        <th>{t('tx_date')}</th>\n                                        <th>{t('sender')}</th>\n                                        <th>{t('receiver')}</th>\n                                        <th>{t('amount')}</th>\n                                        <th>{t('tx_type')}</th>\n                                        <th>{t('filecoin_msg_id')}</th>\n                                        <th>{t('agent')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {transactions.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"9\" className=\"text-center\">{t('no_transactions_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        transactions.map((transaction) => (\n                                            <tr key={transaction.id}>\n                                                <td>{transaction.id.substring(0, 8)}...</td>\n                                                <td>{new Date(transaction.tx_date).toLocaleString()}</td>\n                                                <td>{transaction.sender?.email || '-'}</td>\n                                                <td>{transaction.receiver?.email || '-'}</td>\n                                                <td>{transaction.amount_net ? Number(transaction.amount_net).toFixed(6) : '0'} FIL</td>\n                                                <td>\n                                                    <Badge bg={getTransactionTypeColor(transaction.tx_type)}>\n                                                        {transaction.tx_type || 'unknown'}\n                                                    </Badge>\n                                                </td>\n                                                <td>\n                                                    {transaction.filecoin_msg_id ? (\n                                                        <span title={transaction.filecoin_msg_id}>\n                                                            {transaction.filecoin_msg_id.substring(0, 10)}...\n                                                        </span>\n                                                    ) : '-'}\n                                                </td>\n                                                <td>{transaction.agent?.users?.email || '-'}</td>\n                                                <td>{new Date(transaction.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Transactions;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,cAAc,CAAC,CACpBC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,SAAS,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE3C,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACxD,CAAC,IAAM,CACHT,eAAe,CAACK,IAAI,CAAC,CACzB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAY,uBAAuB,CAAIC,MAAM,EAAK,CACxC,OAAQA,MAAM,EACV,IAAK,SAAS,CACV,MAAO,SAAS,CACpB,IAAK,YAAY,CACb,MAAO,QAAQ,CACnB,IAAK,UAAU,CACX,MAAO,SAAS,CACpB,IAAK,QAAQ,CACT,MAAO,MAAM,CACjB,QACI,MAAO,WAAW,CAC1B,CACJ,CAAC,CAED,GAAIf,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,sBAAsB,CAAC,CAAM,CAAC,CACjD,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAAgC,QAAA,eACNvB,IAAA,OAAIwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEnB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC7CJ,IAAA,CAACR,GAAG,EAAA+B,QAAA,cACAvB,IAAA,CAACP,GAAG,EAAA8B,QAAA,cACAvB,IAAA,CAACN,IAAI,EAAA6B,QAAA,cACDvB,IAAA,CAACN,IAAI,CAAC+B,IAAI,EAAAF,QAAA,cACNrB,KAAA,CAACP,KAAK,EAAC+B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCvB,IAAA,UAAAuB,QAAA,cACIrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,cACvBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,QAAQ,CAAC,CAAK,CAAC,cACtBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,cACvBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,cAC/BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,EAC1B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAuB,QAAA,CACKlB,YAAY,CAACyB,MAAM,GAAK,CAAC,cACtB9B,IAAA,OAAAuB,QAAA,cACIvB,IAAA,OAAI+B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEnB,CAAC,CAAC,2BAA2B,CAAC,CAAK,CAAC,CAC7E,CAAC,CAELC,YAAY,CAAC2B,GAAG,CAAEC,WAAW,OAAAC,mBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,qBAAA,oBACzBnC,KAAA,OAAAqB,QAAA,eACIrB,KAAA,OAAAqB,QAAA,EAAKU,WAAW,CAACK,EAAE,CAACC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,KAAG,EAAI,CAAC,cAC5CvC,IAAA,OAAAuB,QAAA,CAAK,GAAI,CAAAiB,IAAI,CAACP,WAAW,CAACQ,OAAO,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,cACzD1C,IAAA,OAAAuB,QAAA,CAAK,EAAAW,mBAAA,CAAAD,WAAW,CAACU,MAAM,UAAAT,mBAAA,iBAAlBA,mBAAA,CAAoBU,KAAK,GAAI,GAAG,CAAK,CAAC,cAC3C5C,IAAA,OAAAuB,QAAA,CAAK,EAAAY,qBAAA,CAAAF,WAAW,CAACY,QAAQ,UAAAV,qBAAA,iBAApBA,qBAAA,CAAsBS,KAAK,GAAI,GAAG,CAAK,CAAC,cAC7C1C,KAAA,OAAAqB,QAAA,EAAKU,WAAW,CAACa,UAAU,CAAGC,MAAM,CAACd,WAAW,CAACa,UAAU,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,MAAI,EAAI,CAAC,cACvFhD,IAAA,OAAAuB,QAAA,cACIvB,IAAA,CAACJ,KAAK,EAACqD,EAAE,CAAE5B,uBAAuB,CAACY,WAAW,CAACiB,OAAO,CAAE,CAAA3B,QAAA,CACnDU,WAAW,CAACiB,OAAO,EAAI,SAAS,CAC9B,CAAC,CACR,CAAC,cACLlD,IAAA,OAAAuB,QAAA,CACKU,WAAW,CAACkB,eAAe,cACxBjD,KAAA,SAAMkD,KAAK,CAAEnB,WAAW,CAACkB,eAAgB,CAAA5B,QAAA,EACpCU,WAAW,CAACkB,eAAe,CAACZ,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC,KAClD,EAAM,CAAC,CACP,GAAG,CACP,CAAC,cACLvC,IAAA,OAAAuB,QAAA,CAAK,EAAAa,kBAAA,CAAAH,WAAW,CAACoB,KAAK,UAAAjB,kBAAA,kBAAAC,qBAAA,CAAjBD,kBAAA,CAAmBkB,KAAK,UAAAjB,qBAAA,iBAAxBA,qBAAA,CAA0BO,KAAK,GAAI,GAAG,CAAK,CAAC,cACjD5C,IAAA,OAAAuB,QAAA,CAAK,GAAI,CAAAiB,IAAI,CAACP,WAAW,CAACsB,UAAU,CAAC,CAACb,cAAc,CAAC,CAAC,CAAK,CAAC,GAnBvDT,WAAW,CAACK,EAoBjB,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAnC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}