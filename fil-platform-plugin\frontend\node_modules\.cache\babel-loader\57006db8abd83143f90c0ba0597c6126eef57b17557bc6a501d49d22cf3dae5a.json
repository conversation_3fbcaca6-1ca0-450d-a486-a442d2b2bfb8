{"ast": null, "code": "import React,{Suspense,useEffect,useState}from'react';import{initSupabase}from'./supabaseClient';import{HashRouter,Routes,Route,Link,Navigate,useLocation}from'react-router-dom';import{Container,Navbar,Nav,NavDropdown}from'react-bootstrap';import{useTranslation}from'react-i18next';import{FaTachometerAlt,FaHardHat,FaGlobe,FaCoins,FaChartBar}from'react-icons/fa';// Lazy load components for better performance\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=/*#__PURE__*/React.lazy(()=>import('./pages/LoginPage'));const CustomerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/customer/Dashboard'));const ProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/ProductListPage'));const OrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/OrderListPage'));const WalletPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/WalletPage'));const MyAccountPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyAccountPage'));const MyGainsPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/MyGainsPage'));const KycPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/KycPage'));const RecommendPage=/*#__PURE__*/React.lazy(()=>import('./pages/customer/RecommendPage'));const AgentDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/agent/Dashboard'));const AgentMemberList=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentMemberList'));const AgentProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/agent/AgentProductListPage'));const MakerDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Dashboard'));const MakerProductListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerProductListPage'));const MakerOrderListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerOrderListPage'));const MakerFacilityListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerFacilities'));const MakerMinerListPage=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MakerMiners'));const MinerEarnings=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerEarnings'));const MinerSnapshots=/*#__PURE__*/React.lazy(()=>import('./pages/maker/MinerSnapshots'));const Transactions=/*#__PURE__*/React.lazy(()=>import('./pages/maker/Transactions'));function App(){const{t,i18n}=useTranslation();const[supabase,setSupabase]=useState(null);const[session,setSession]=useState(null);const[loading,setLoading]=useState(true);const role=localStorage.getItem('user_role');// 从 localStorage 读取用户角色\nuseEffect(()=>{const initialize=async()=>{const supa=await initSupabase();setSupabase(supa);const{data:{session}}=await supa.auth.getSession();setSession(session);supa.auth.onAuthStateChange((_event,newSession)=>{setSession(newSession);if(!newSession){localStorage.removeItem('user_role');}});setLoading(false);};initialize();},[]);const changeLanguage=lng=>{i18n.changeLanguage(lng);};// Debug: Log current URL and hash\nReact.useEffect(()=>{console.log('App mounted. Current URL:',window.location.href);console.log('Hash:',window.location.hash);},[]);// Require login to access protected pages\nconst RequireAuth=_ref=>{let{children}=_ref;const location=useLocation();if(!session){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}return children;};// Auto redirect from \"/\" based on role\nconst RoleRedirect=()=>{const role=localStorage.getItem('user_role');if(role==='maker')return/*#__PURE__*/_jsx(Navigate,{to:\"/maker\",replace:true});if(role==='agent')return/*#__PURE__*/_jsx(Navigate,{to:\"/agent\",replace:true});return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});// default to login page\n};return/*#__PURE__*/_jsx(HashRouter,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Navbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Navbar.Brand,{as:Link,to:\"/\",children:\"FIL Platform\"}),/*#__PURE__*/_jsx(Navbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(Navbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsxs(Nav,{className:\"me-auto\",children:[role==='maker'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaHardHat,{className:\"me-1\"}),t('miner_management')]}),id:\"maker-miner-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/miners\",children:t('miner_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/facilities\",children:t('facility_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/earnings\",children:t('earnings_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/transfers\",children:t('transfer_list')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/snapshots\",children:t('daily_snapshot')})]}),/*#__PURE__*/_jsxs(NavDropdown,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaGlobe,{className:\"me-1\"}),t('operations_management')]}),id:\"maker-operations-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/capacity\",children:t('capacity_expansion_request')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/orders\",children:t('maker_orders')}),/*#__PURE__*/_jsx(NavDropdown.Item,{as:Link,to:\"/maker/manual-deposits\",children:t('manual_deposit')})]}),/*#__PURE__*/_jsxs(Nav.Link,{as:Link,to:\"/maker/coins\",children:[/*#__PURE__*/_jsx(FaCoins,{className:\"me-1\"}),t('coin_management')]}),/*#__PURE__*/_jsxs(Nav.Link,{as:Link,to:\"/maker/reports\",children:[/*#__PURE__*/_jsx(FaChartBar,{className:\"me-1\"}),t('report_management')]})]}),/*#__PURE__*/_jsxs(Nav.Link,{href:\"#/\",children:[/*#__PURE__*/_jsx(FaTachometerAlt,{className:\"me-1\"}),t('dashboard')]})]}),/*#__PURE__*/_jsx(Nav,{children:/*#__PURE__*/_jsxs(NavDropdown,{title:t('language'),id:\"basic-nav-dropdown\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('ja'),children:\"\\u65E5\\u672C\\u8A9E\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('zh'),children:\"\\u4E2D\\u6587\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>changeLanguage('en'),children:\"English\"})]})})]})]})}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(\"div\",{children:t('loading')}),children:loading?/*#__PURE__*/_jsx(\"div\",{children:t('initializing_platform')}):!supabase?/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-danger\",children:t('backend_connection_failed')}):/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RoleRedirect,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/customer\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(CustomerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(ProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(OrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/wallet\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(WalletPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyAccountPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my-gains\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MyGainsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/kyc\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(KycPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/my/recommend\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(RecommendPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/members\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentMemberList,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/agent/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(AgentProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/products\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerProductListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/orders\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerOrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/facilities\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerFacilityListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/miners\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MakerMinerListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/earnings\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerEarnings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/transfers\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(Transactions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/maker/snapshots\",element:/*#__PURE__*/_jsx(RequireAuth,{children:/*#__PURE__*/_jsx(MinerSnapshots,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})})})]})});}export default App;", "map": {"version": 3, "names": ["React", "Suspense", "useEffect", "useState", "initSupabase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Link", "Navigate", "useLocation", "Container", "<PERSON><PERSON><PERSON>", "Nav", "NavDropdown", "useTranslation", "FaTachometerAlt", "FaHardHat", "FaGlobe", "FaCoins", "FaChartBar", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "LoginPage", "lazy", "CustomerDashboard", "ProductListPage", "OrderListPage", "WalletPage", "MyAccountPage", "MyGainsPage", "KycPage", "RecommendPage", "AgentDashboard", "AgentMemberList", "AgentProductListPage", "MakerDashboard", "MakerProductListPage", "MakerOrderListPage", "MakerFacilityListPage", "MakerMinerListPage", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "MinerSnapshots", "Transactions", "App", "t", "i18n", "supabase", "set<PERSON><PERSON><PERSON><PERSON>", "session", "setSession", "loading", "setLoading", "role", "localStorage", "getItem", "initialize", "supa", "data", "auth", "getSession", "onAuthStateChange", "_event", "newSession", "removeItem", "changeLanguage", "lng", "console", "log", "window", "location", "href", "hash", "RequireAuth", "_ref", "children", "to", "state", "from", "replace", "RoleRedirect", "bg", "variant", "expand", "Brand", "as", "Toggle", "Collapse", "id", "className", "title", "<PERSON><PERSON>", "onClick", "fallback", "path", "element"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/App.js"], "sourcesContent": ["import React, { Suspense, useEffect, useState } from 'react';\nimport { initSupabase } from './supabaseClient';\nimport {\n  HashRouter,\n  Routes,\n  Route,\n  Link,\n  Navigate,\n  useLocation,\n} from 'react-router-dom';\nimport { Container, Navbar, Nav, NavDropdown } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { FaTachometerAlt, FaHardHat, FaGlobe, FaCoins, FaChartBar } from 'react-icons/fa';\n\n// Lazy load components for better performance\nconst LoginPage = React.lazy(() => import('./pages/LoginPage'));\nconst CustomerDashboard = React.lazy(() => import('./pages/customer/Dashboard'));\nconst ProductListPage = React.lazy(() => import('./pages/customer/ProductListPage'));\nconst OrderListPage = React.lazy(() => import('./pages/customer/OrderListPage'));\nconst WalletPage = React.lazy(() => import('./pages/customer/WalletPage'));\nconst MyAccountPage = React.lazy(() => import('./pages/customer/MyAccountPage'));\nconst MyGainsPage = React.lazy(() => import('./pages/customer/MyGainsPage'));\nconst KycPage = React.lazy(() => import('./pages/customer/KycPage'));\nconst RecommendPage = React.lazy(() => import('./pages/customer/RecommendPage'));\nconst AgentDashboard = React.lazy(() => import('./pages/agent/Dashboard'));\nconst AgentMemberList = React.lazy(() => import('./pages/agent/AgentMemberList'));\nconst AgentProductListPage = React.lazy(() => import('./pages/agent/AgentProductListPage'));\nconst MakerDashboard = React.lazy(() => import('./pages/maker/Dashboard'));\nconst MakerProductListPage = React.lazy(() => import('./pages/maker/MakerProductListPage'));\nconst MakerOrderListPage = React.lazy(() => import('./pages/maker/MakerOrderListPage'));\nconst MakerFacilityListPage = React.lazy(() => import('./pages/maker/MakerFacilities'));\nconst MakerMinerListPage = React.lazy(() => import('./pages/maker/MakerMiners'));\nconst MinerEarnings = React.lazy(() => import('./pages/maker/MinerEarnings'));\nconst MinerSnapshots = React.lazy(() => import('./pages/maker/MinerSnapshots'));\nconst Transactions = React.lazy(() => import('./pages/maker/Transactions'));\n\nfunction App() {\n  const { t, i18n } = useTranslation();\n  const [supabase, setSupabase] = useState(null);\n  const [session, setSession] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const role = localStorage.getItem('user_role'); // 从 localStorage 读取用户角色\n\n  useEffect(() => {\n    const initialize = async () => {\n      const supa = await initSupabase();\n      setSupabase(supa);\n\n      const { data: { session } } = await supa.auth.getSession();\n      setSession(session);\n\n      supa.auth.onAuthStateChange((_event, newSession) => {\n        setSession(newSession);\n        if (!newSession) {\n          localStorage.removeItem('user_role');\n        }\n      });\n\n      setLoading(false);\n    };\n    initialize();\n  }, []);\n\n  const changeLanguage = (lng) => {\n    i18n.changeLanguage(lng);\n  };\n\n  // Debug: Log current URL and hash\n  React.useEffect(() => {\n    console.log('App mounted. Current URL:', window.location.href);\n    console.log('Hash:', window.location.hash);\n  }, []);\n\n  // Require login to access protected pages\n  const RequireAuth = ({ children }) => {\n    const location = useLocation();\n    if (!session) {\n      return <Navigate to=\"/login\" state={{ from: location }} replace />;\n    }\n    return children;\n  };\n\n  // Auto redirect from \"/\" based on role\n  const RoleRedirect = () => {\n    const role = localStorage.getItem('user_role');\n    if (role === 'maker') return <Navigate to=\"/maker\" replace />;\n    if (role === 'agent') return <Navigate to=\"/agent\" replace />;\n    return <Navigate to=\"/login\" replace />; // default to login page\n  };\n\n  return (\n    <HashRouter>\n      <div>\n        <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\">\n          <Container>\n            <Navbar.Brand as={Link} to=\"/\">FIL Platform</Navbar.Brand>\n            <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n            <Navbar.Collapse id=\"basic-navbar-nav\">\n              <Nav className=\"me-auto\">\n                {/* ===== ★ Maker 导航开始 ★ ===== */}\n                {role === 'maker' && (\n                  <>\n                    {/* Miner Management 下拉 */}\n                    <NavDropdown\n                      title={\n                        <>\n                          <FaHardHat className=\"me-1\" />\n                          {t('miner_management')}\n                        </>\n                      }\n                      id=\"maker-miner-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/miners\">\n                        {t('miner_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/facilities\">\n                        {t('facility_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/earnings\">\n                        {t('earnings_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/transfers\">\n                        {t('transfer_list')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/snapshots\">\n                        {t('daily_snapshot')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    {/* 其余一级菜单 */}\n                    <NavDropdown title={\n                        <>\n                          <FaGlobe className=\"me-1\" />\n                          {t('operations_management')}\n                        </>\n                      }\n                      id=\"maker-operations-dropdown\"\n                    >\n                      <NavDropdown.Item as={Link} to=\"/maker/capacity\">\n                        {t('capacity_expansion_request')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/orders\">\n                        {t('maker_orders')}\n                      </NavDropdown.Item>\n                      <NavDropdown.Item as={Link} to=\"/maker/manual-deposits\">\n                        {t('manual_deposit')}\n                      </NavDropdown.Item>\n                    </NavDropdown>\n\n                    <Nav.Link as={Link} to=\"/maker/coins\">\n                      <FaCoins className=\"me-1\" />\n                      {t('coin_management')}\n                    </Nav.Link>\n\n                    <Nav.Link as={Link} to=\"/maker/reports\">\n                      <FaChartBar className=\"me-1\" />\n                      {t('report_management')}\n                    </Nav.Link>\n                  </>\n                )}\n                {/* ===== ★ Maker 导航结束 ★ ===== */}\n                <Nav.Link href=\"#/\">\n                  <FaTachometerAlt className=\"me-1\" />\n                  {t('dashboard')}\n                </Nav.Link>\n                {/* Add other nav links based on role later */}\n              </Nav>\n              <Nav>\n                <NavDropdown title={t('language')} id=\"basic-nav-dropdown\">\n                  <NavDropdown.Item onClick={() => changeLanguage('ja')}>日本語</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('zh')}>中文</NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => changeLanguage('en')}>English</NavDropdown.Item>\n                </NavDropdown>\n              </Nav>\n            </Navbar.Collapse>\n          </Container>\n        </Navbar>\n\n        <Container className=\"mt-4\">\n          <Suspense fallback={<div>{t('loading')}</div>}>\n            {loading ? (\n              <div>{t('initializing_platform')}</div>\n            ) : !supabase ? (\n              <div className=\"alert alert-danger\">{t('backend_connection_failed')}</div>\n            ) : (\n              <Routes>\n                {/* Public Route */}\n                <Route path=\"/login\" element={<LoginPage />} />\n\n                {/* Root path → redirect by role */}\n                <Route path=\"/\" element={<RequireAuth><RoleRedirect /></RequireAuth>} />\n\n                {/* Customer Routes */}\n                <Route path=\"/customer\" element={<RequireAuth><CustomerDashboard /></RequireAuth>} />\n                <Route path=\"/products\" element={<RequireAuth><ProductListPage /></RequireAuth>} />\n                <Route path=\"/orders\" element={<RequireAuth><OrderListPage /></RequireAuth>} />\n                <Route path=\"/wallet\" element={<RequireAuth><WalletPage /></RequireAuth>} />\n                <Route path=\"/my\" element={<RequireAuth><MyAccountPage /></RequireAuth>} />\n                <Route path=\"/my-gains\" element={<RequireAuth><MyGainsPage /></RequireAuth>} />\n                <Route path=\"/my/kyc\" element={<RequireAuth><KycPage /></RequireAuth>} />\n                <Route path=\"/my/recommend\" element={<RequireAuth><RecommendPage /></RequireAuth>} />\n\n                {/* Agent Routes */}\n                <Route path=\"/agent\" element={<RequireAuth><AgentDashboard /></RequireAuth>} />\n                <Route path=\"/agent/members\" element={<RequireAuth><AgentMemberList /></RequireAuth>} />\n                <Route path=\"/agent/products\" element={<RequireAuth><AgentProductListPage /></RequireAuth>} />\n\n                {/* Maker Routes */}\n                  <Route path=\"/maker\" element={<RequireAuth><MakerDashboard /></RequireAuth>} />\n                  <Route path=\"/maker/products\" element={<RequireAuth><MakerProductListPage /></RequireAuth>} />\n                  <Route path=\"/maker/orders\" element={<RequireAuth><MakerOrderListPage /></RequireAuth>} />\n                  <Route path=\"/maker/facilities\" element={<RequireAuth><MakerFacilityListPage /></RequireAuth>} />\n                  <Route path=\"/maker/miners\" element={<RequireAuth><MakerMinerListPage /></RequireAuth>} />\n                  <Route path=\"/maker/earnings\" element={<RequireAuth><MinerEarnings /></RequireAuth>} />\n                  <Route path=\"/maker/transfers\" element={<RequireAuth><Transactions /></RequireAuth>} />\n                  <Route path=\"/maker/snapshots\" element={<RequireAuth><MinerSnapshots /></RequireAuth>} />\n                \n                {/* Fallback */}\n                <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n              </Routes>\n            )}\n          </Suspense>\n        </Container>\n      </div>\n    </HashRouter>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC5D,OAASC,YAAY,KAAQ,kBAAkB,CAC/C,OACEC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,WAAW,KACN,kBAAkB,CACzB,OAASC,SAAS,CAAEC,MAAM,CAAEC,GAAG,CAAEC,WAAW,KAAQ,iBAAiB,CACrE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,eAAe,CAAEC,SAAS,CAAEC,OAAO,CAAEC,OAAO,CAAEC,UAAU,KAAQ,gBAAgB,CAEzF;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,SAAS,cAAG3B,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAC,iBAAiB,cAAG7B,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAChF,KAAM,CAAAE,eAAe,cAAG9B,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACpF,KAAM,CAAAG,aAAa,cAAG/B,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAI,UAAU,cAAGhC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC1E,KAAM,CAAAK,aAAa,cAAGjC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAM,WAAW,cAAGlC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC5E,KAAM,CAAAO,OAAO,cAAGnC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CACpE,KAAM,CAAAQ,aAAa,cAAGpC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAChF,KAAM,CAAAS,cAAc,cAAGrC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAU,eAAe,cAAGtC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACjF,KAAM,CAAAW,oBAAoB,cAAGvC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAY,cAAc,cAAGxC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC1E,KAAM,CAAAa,oBAAoB,cAAGzC,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC3F,KAAM,CAAAc,kBAAkB,cAAG1C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACvF,KAAM,CAAAe,qBAAqB,cAAG3C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACvF,KAAM,CAAAgB,kBAAkB,cAAG5C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAChF,KAAM,CAAAiB,aAAa,cAAG7C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAC7E,KAAM,CAAAkB,cAAc,cAAG9C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC/E,KAAM,CAAAmB,YAAY,cAAG/C,KAAK,CAAC4B,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAE3E,QAAS,CAAAoB,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGnC,cAAc,CAAC,CAAC,CACpC,KAAM,CAACoC,QAAQ,CAAEC,WAAW,CAAC,CAAGjD,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACkD,OAAO,CAAEC,UAAU,CAAC,CAAGnD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACoD,OAAO,CAAEC,UAAU,CAAC,CAAGrD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAAsD,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAE;AAEhDzD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA0D,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAzD,YAAY,CAAC,CAAC,CACjCgD,WAAW,CAACS,IAAI,CAAC,CAEjB,KAAM,CAAEC,IAAI,CAAE,CAAET,OAAQ,CAAE,CAAC,CAAG,KAAM,CAAAQ,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,CAAC,CAC1DV,UAAU,CAACD,OAAO,CAAC,CAEnBQ,IAAI,CAACE,IAAI,CAACE,iBAAiB,CAAC,CAACC,MAAM,CAAEC,UAAU,GAAK,CAClDb,UAAU,CAACa,UAAU,CAAC,CACtB,GAAI,CAACA,UAAU,CAAE,CACfT,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC,CACtC,CACF,CAAC,CAAC,CAEFZ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CACDI,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,cAAc,CAAIC,GAAG,EAAK,CAC9BpB,IAAI,CAACmB,cAAc,CAACC,GAAG,CAAC,CAC1B,CAAC,CAED;AACAtE,KAAK,CAACE,SAAS,CAAC,IAAM,CACpBqE,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAC9DJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEC,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAC,CAC5C,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC/B,KAAM,CAAAJ,QAAQ,CAAGhE,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAC2C,OAAO,CAAE,CACZ,mBAAO/B,IAAA,CAACb,QAAQ,EAACuE,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAER,QAAS,CAAE,CAACS,OAAO,MAAE,CAAC,CACpE,CACA,MAAO,CAAAJ,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAA3B,IAAI,CAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAC9C,GAAIF,IAAI,GAAK,OAAO,CAAE,mBAAOnC,IAAA,CAACb,QAAQ,EAACuE,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,GAAI1B,IAAI,GAAK,OAAO,CAAE,mBAAOnC,IAAA,CAACb,QAAQ,EAACuE,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAC7D,mBAAO7D,IAAA,CAACb,QAAQ,EAACuE,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CAAE;AAC3C,CAAC,CAED,mBACE7D,IAAA,CAACjB,UAAU,EAAA0E,QAAA,cACTrD,KAAA,QAAAqD,QAAA,eACEzD,IAAA,CAACV,MAAM,EAACyE,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAAR,QAAA,cAC1CrD,KAAA,CAACf,SAAS,EAAAoE,QAAA,eACRzD,IAAA,CAACV,MAAM,CAAC4E,KAAK,EAACC,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,GAAG,CAAAD,QAAA,CAAC,cAAY,CAAc,CAAC,cAC1DzD,IAAA,CAACV,MAAM,CAAC8E,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAClDhE,KAAA,CAACd,MAAM,CAAC+E,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAb,QAAA,eACpCrD,KAAA,CAACb,GAAG,EAACgF,SAAS,CAAC,SAAS,CAAAd,QAAA,EAErBtB,IAAI,GAAK,OAAO,eACf/B,KAAA,CAAAF,SAAA,EAAAuD,QAAA,eAEErD,KAAA,CAACZ,WAAW,EACVgF,KAAK,cACHpE,KAAA,CAAAF,SAAA,EAAAuD,QAAA,eACEzD,IAAA,CAACL,SAAS,EAAC4E,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7B5C,CAAC,CAAC,kBAAkB,CAAC,EACtB,CACH,CACD2C,EAAE,CAAC,sBAAsB,CAAAb,QAAA,eAEzBzD,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACN,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,YAAY,CAAC,CACA,CAAC,cACnB3B,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACN,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAC/C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnB3B,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACN,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnB3B,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACN,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9C9B,CAAC,CAAC,eAAe,CAAC,CACH,CAAC,cACnB3B,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACN,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,kBAAkB,CAAAD,QAAA,CAC9C9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAGdvB,KAAA,CAACZ,WAAW,EAACgF,KAAK,cACdpE,KAAA,CAAAF,SAAA,EAAAuD,QAAA,eACEzD,IAAA,CAACJ,OAAO,EAAC2E,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B5C,CAAC,CAAC,uBAAuB,CAAC,EAC3B,CACH,CACD2C,EAAE,CAAC,2BAA2B,CAAAb,QAAA,eAE9BzD,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACN,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,iBAAiB,CAAAD,QAAA,CAC7C9B,CAAC,CAAC,4BAA4B,CAAC,CAChB,CAAC,cACnB3B,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACN,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,eAAe,CAAAD,QAAA,CAC3C9B,CAAC,CAAC,cAAc,CAAC,CACF,CAAC,cACnB3B,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACN,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,wBAAwB,CAAAD,QAAA,CACpD9B,CAAC,CAAC,gBAAgB,CAAC,CACJ,CAAC,EACR,CAAC,cAEdvB,KAAA,CAACb,GAAG,CAACL,IAAI,EAACiF,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,cAAc,CAAAD,QAAA,eACnCzD,IAAA,CAACH,OAAO,EAAC0E,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B5C,CAAC,CAAC,iBAAiB,CAAC,EACb,CAAC,cAEXvB,KAAA,CAACb,GAAG,CAACL,IAAI,EAACiF,EAAE,CAAEjF,IAAK,CAACwE,EAAE,CAAC,gBAAgB,CAAAD,QAAA,eACrCzD,IAAA,CAACF,UAAU,EAACyE,SAAS,CAAC,MAAM,CAAE,CAAC,CAC9B5C,CAAC,CAAC,mBAAmB,CAAC,EACf,CAAC,EACX,CACH,cAEDvB,KAAA,CAACb,GAAG,CAACL,IAAI,EAACmE,IAAI,CAAC,IAAI,CAAAI,QAAA,eACjBzD,IAAA,CAACN,eAAe,EAAC6E,SAAS,CAAC,MAAM,CAAE,CAAC,CACnC5C,CAAC,CAAC,WAAW,CAAC,EACP,CAAC,EAER,CAAC,cACN3B,IAAA,CAACT,GAAG,EAAAkE,QAAA,cACFrD,KAAA,CAACZ,WAAW,EAACgF,KAAK,CAAE7C,CAAC,CAAC,UAAU,CAAE,CAAC2C,EAAE,CAAC,oBAAoB,CAAAb,QAAA,eACxDzD,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAM3B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,oBAAG,CAAkB,CAAC,cAC7EzD,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAM3B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,cAAE,CAAkB,CAAC,cAC5EzD,IAAA,CAACR,WAAW,CAACiF,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAM3B,cAAc,CAAC,IAAI,CAAE,CAAAU,QAAA,CAAC,SAAO,CAAkB,CAAC,EACtE,CAAC,CACX,CAAC,EACS,CAAC,EACT,CAAC,CACN,CAAC,cAETzD,IAAA,CAACX,SAAS,EAACkF,SAAS,CAAC,MAAM,CAAAd,QAAA,cACzBzD,IAAA,CAACrB,QAAQ,EAACgG,QAAQ,cAAE3E,IAAA,QAAAyD,QAAA,CAAM9B,CAAC,CAAC,SAAS,CAAC,CAAM,CAAE,CAAA8B,QAAA,CAC3CxB,OAAO,cACNjC,IAAA,QAAAyD,QAAA,CAAM9B,CAAC,CAAC,uBAAuB,CAAC,CAAM,CAAC,CACrC,CAACE,QAAQ,cACX7B,IAAA,QAAKuE,SAAS,CAAC,oBAAoB,CAAAd,QAAA,CAAE9B,CAAC,CAAC,2BAA2B,CAAC,CAAM,CAAC,cAE1EvB,KAAA,CAACpB,MAAM,EAAAyE,QAAA,eAELzD,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE7E,IAAA,CAACK,SAAS,GAAE,CAAE,CAAE,CAAC,cAG/CL,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAAC8D,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGxE9D,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACO,iBAAiB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACrFP,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACQ,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFR,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,SAAS,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACS,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/ET,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,SAAS,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACU,UAAU,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC5EV,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,KAAK,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACW,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3EX,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACY,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/EZ,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,SAAS,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACa,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzEb,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACc,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGrFd,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACe,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/Ef,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACgB,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACxFhB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACiB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAG5FjB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACkB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC/ElB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACmB,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC9FnB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACoB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FpB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACqB,qBAAqB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACjGrB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACsB,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC1FtB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACuB,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFvB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACyB,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFzB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAE7E,IAAA,CAACuD,WAAW,EAAAE,QAAA,cAACzD,IAAA,CAACwB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAG3FxB,IAAA,CAACf,KAAK,EAAC2F,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE7E,IAAA,CAACb,QAAQ,EAACuE,EAAE,CAAC,GAAG,CAACG,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CACT,CACO,CAAC,CACF,CAAC,EACT,CAAC,CACI,CAAC,CAEjB,CAEA,cAAe,CAAAnC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}