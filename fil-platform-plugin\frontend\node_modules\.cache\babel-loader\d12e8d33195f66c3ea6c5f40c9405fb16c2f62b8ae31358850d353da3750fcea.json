{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MinerSnapshots=()=>{const{t}=useTranslation();const[snapshots,setSnapshots]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchSnapshots=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch miner daily snapshots with miner information\nconst{data,error}=await supabase.from('miner_daily_snapshots').select(`\n                    miner_id,\n                    snapshot_date,\n                    blockchain_height,\n                    power,\n                    available_balance,\n                    pledge_locked,\n                    balance,\n                    miners (\n                        filecoin_miner_id,\n                        category,\n                        facilities (\n                            name\n                        )\n                    )\n                `).order('snapshot_date',{ascending:false});if(error){console.error('Error fetching miner snapshots:',error);}else{setSnapshots(data);}setLoading(false);};fetchSnapshots();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_snapshots')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('miner_snapshots')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('miner_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('facility')}),/*#__PURE__*/_jsx(\"th\",{children:t('snapshot_date')}),/*#__PURE__*/_jsx(\"th\",{children:t('blockchain_height')}),/*#__PURE__*/_jsx(\"th\",{children:t('power')}),/*#__PURE__*/_jsx(\"th\",{children:t('available_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('pledge_locked')}),/*#__PURE__*/_jsx(\"th\",{children:t('balance')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:snapshots.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"8\",className:\"text-center\",children:t('no_snapshots_available')})}):snapshots.map((snapshot,index)=>{var _snapshot$miners,_snapshot$miners2,_snapshot$miners2$fac;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:((_snapshot$miners=snapshot.miners)===null||_snapshot$miners===void 0?void 0:_snapshot$miners.filecoin_miner_id)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_snapshot$miners2=snapshot.miners)===null||_snapshot$miners2===void 0?void 0:(_snapshot$miners2$fac=_snapshot$miners2.facilities)===null||_snapshot$miners2$fac===void 0?void 0:_snapshot$miners2$fac.name)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:new Date(snapshot.snapshot_date).toLocaleDateString()}),/*#__PURE__*/_jsx(\"td\",{children:snapshot.blockchain_height||'-'}),/*#__PURE__*/_jsxs(\"td\",{children:[snapshot.power?Number(snapshot.power).toFixed(2):'0',\" TiB\"]}),/*#__PURE__*/_jsxs(\"td\",{children:[snapshot.available_balance?Number(snapshot.available_balance).toFixed(6):'0',\" FIL\"]}),/*#__PURE__*/_jsxs(\"td\",{children:[snapshot.pledge_locked?Number(snapshot.pledge_locked).toFixed(6):'0',\" FIL\"]}),/*#__PURE__*/_jsxs(\"td\",{children:[snapshot.balance?Number(snapshot.balance).toFixed(6):'0',\" FIL\"]})]},`${snapshot.miner_id}-${snapshot.snapshot_date}`);})})]})})})})})]});};export default MinerSnapshots;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "MinerSnapshots", "t", "snapshots", "setSnapshots", "loading", "setLoading", "fetchSnapshots", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "snapshot", "index", "_snapshot$miners", "_snapshot$miners2", "_snapshot$miners2$fac", "miners", "filecoin_miner_id", "facilities", "name", "Date", "snapshot_date", "toLocaleDateString", "blockchain_height", "power", "Number", "toFixed", "available_balance", "pledge_locked", "balance", "miner_id"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/MinerSnapshots.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst MinerSnapshots = () => {\n    const { t } = useTranslation();\n    const [snapshots, setSnapshots] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchSnapshots = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch miner daily snapshots with miner information\n            const { data, error } = await supabase\n                .from('miner_daily_snapshots')\n                .select(`\n                    miner_id,\n                    snapshot_date,\n                    blockchain_height,\n                    power,\n                    available_balance,\n                    pledge_locked,\n                    balance,\n                    miners (\n                        filecoin_miner_id,\n                        category,\n                        facilities (\n                            name\n                        )\n                    )\n                `)\n                .order('snapshot_date', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching miner snapshots:', error);\n            } else {\n                setSnapshots(data);\n            }\n            setLoading(false);\n        };\n\n        fetchSnapshots();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_snapshots')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('miner_snapshots')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('miner_id')}</th>\n                                        <th>{t('facility')}</th>\n                                        <th>{t('snapshot_date')}</th>\n                                        <th>{t('blockchain_height')}</th>\n                                        <th>{t('power')}</th>\n                                        <th>{t('available_balance')}</th>\n                                        <th>{t('pledge_locked')}</th>\n                                        <th>{t('balance')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {snapshots.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"8\" className=\"text-center\">{t('no_snapshots_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        snapshots.map((snapshot, index) => (\n                                            <tr key={`${snapshot.miner_id}-${snapshot.snapshot_date}`}>\n                                                <td>{snapshot.miners?.filecoin_miner_id || '-'}</td>\n                                                <td>{snapshot.miners?.facilities?.name || '-'}</td>\n                                                <td>{new Date(snapshot.snapshot_date).toLocaleDateString()}</td>\n                                                <td>{snapshot.blockchain_height || '-'}</td>\n                                                <td>{snapshot.power ? Number(snapshot.power).toFixed(2) : '0'} TiB</td>\n                                                <td>{snapshot.available_balance ? Number(snapshot.available_balance).toFixed(6) : '0'} FIL</td>\n                                                <td>{snapshot.pledge_locked ? Number(snapshot.pledge_locked).toFixed(6) : '0'} FIL</td>\n                                                <td>{snapshot.balance ? Number(snapshot.balance).toFixed(6) : '0'} FIL</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MinerSnapshots;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,cAAc,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,uBAAuB,CAAC,CAC7BC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,eAAe,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAEjD,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CAC3D,CAAC,IAAM,CACHT,YAAY,CAACK,IAAI,CAAC,CACtB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,cAAc,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAqB,QAAA,CAAMjB,CAAC,CAAC,mBAAmB,CAAC,CAAM,CAAC,CAC9C,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAA8B,QAAA,eACNrB,IAAA,OAAIsB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEjB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,cAChDJ,IAAA,CAACR,GAAG,EAAA6B,QAAA,cACArB,IAAA,CAACP,GAAG,EAAA4B,QAAA,cACArB,IAAA,CAACN,IAAI,EAAA2B,QAAA,cACDrB,IAAA,CAACN,IAAI,CAAC6B,IAAI,EAAAF,QAAA,cACNnB,KAAA,CAACP,KAAK,EAAC6B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCrB,IAAA,UAAAqB,QAAA,cACInB,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,OAAO,CAAC,CAAK,CAAC,cACrBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,EACvB,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAqB,QAAA,CACKhB,SAAS,CAACuB,MAAM,GAAK,CAAC,cACnB5B,IAAA,OAAAqB,QAAA,cACIrB,IAAA,OAAI6B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEjB,CAAC,CAAC,wBAAwB,CAAC,CAAK,CAAC,CAC1E,CAAC,CAELC,SAAS,CAACyB,GAAG,CAAC,CAACC,QAAQ,CAAEC,KAAK,QAAAC,gBAAA,CAAAC,iBAAA,CAAAC,qBAAA,oBAC1BjC,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAK,EAAAY,gBAAA,CAAAF,QAAQ,CAACK,MAAM,UAAAH,gBAAA,iBAAfA,gBAAA,CAAiBI,iBAAiB,GAAI,GAAG,CAAK,CAAC,cACpDrC,IAAA,OAAAqB,QAAA,CAAK,EAAAa,iBAAA,CAAAH,QAAQ,CAACK,MAAM,UAAAF,iBAAA,kBAAAC,qBAAA,CAAfD,iBAAA,CAAiBI,UAAU,UAAAH,qBAAA,iBAA3BA,qBAAA,CAA6BI,IAAI,GAAI,GAAG,CAAK,CAAC,cACnDvC,IAAA,OAAAqB,QAAA,CAAK,GAAI,CAAAmB,IAAI,CAACT,QAAQ,CAACU,aAAa,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAK,CAAC,cAChE1C,IAAA,OAAAqB,QAAA,CAAKU,QAAQ,CAACY,iBAAiB,EAAI,GAAG,CAAK,CAAC,cAC5CzC,KAAA,OAAAmB,QAAA,EAAKU,QAAQ,CAACa,KAAK,CAAGC,MAAM,CAACd,QAAQ,CAACa,KAAK,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,MAAI,EAAI,CAAC,cACvE5C,KAAA,OAAAmB,QAAA,EAAKU,QAAQ,CAACgB,iBAAiB,CAAGF,MAAM,CAACd,QAAQ,CAACgB,iBAAiB,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,MAAI,EAAI,CAAC,cAC/F5C,KAAA,OAAAmB,QAAA,EAAKU,QAAQ,CAACiB,aAAa,CAAGH,MAAM,CAACd,QAAQ,CAACiB,aAAa,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,MAAI,EAAI,CAAC,cACvF5C,KAAA,OAAAmB,QAAA,EAAKU,QAAQ,CAACkB,OAAO,CAAGJ,MAAM,CAACd,QAAQ,CAACkB,OAAO,CAAC,CAACH,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,MAAI,EAAI,CAAC,GARtE,GAAGf,QAAQ,CAACmB,QAAQ,IAAInB,QAAQ,CAACU,aAAa,EASnD,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAtC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}