"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[668],{668:(e,s,r)=>{r.r(s),r.d(s,{default:()=>f});var a=r(5043),d=r(3519),l=r(1072),t=r(8602),c=r(8628),n=r(4282),i=r(4117),o=r(1283),m=r(4312),x=r(579);const f=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)(null),[f,h]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,m.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:d}=await e.from("maker_profiles").select("*").eq("user_id",s.id).single();d?console.error("Error fetching maker profile:",d):r(a),h(!1)})()},[]),f?(0,x.jsx)("div",{children:e("loading_maker_dashboard")}):s?(0,x.jsx)("div",{style:{display:"flex"},children:(0,x.jsx)("div",{style:{margin:4,flex:1,width:"100%"},children:(0,x.jsxs)(d.A,{fluid:!0,children:[(0,x.jsx)(l.A,{className:"mb-3",children:(0,x.jsx)(t.A,{children:(0,x.jsx)("h3",{children:e("maker_dashboard")})})}),(0,x.jsxs)(l.A,{children:[(0,x.jsx)(t.A,{md:4,children:(0,x.jsx)(c.A,{className:"card-primary text-white mb-3",children:(0,x.jsxs)(c.A.Body,{children:[(0,x.jsx)(c.A.Title,{children:e("domain")}),(0,x.jsx)("h3",{children:s.domain||"N/A"})]})})}),(0,x.jsx)(t.A,{md:4,children:(0,x.jsx)(c.A,{className:"card-success text-white mb-3",children:(0,x.jsxs)(c.A.Body,{children:[(0,x.jsx)(c.A.Title,{children:e("support_email")}),(0,x.jsx)("h3",{children:s.support_email||"N/A"})]})})}),(0,x.jsx)(t.A,{md:4,children:(0,x.jsx)(c.A,{className:"card-info text-white mb-3",children:(0,x.jsxs)(c.A.Body,{children:[(0,x.jsx)(c.A.Title,{children:e("sms_signature")}),(0,x.jsx)("h3",{children:s.sms_signature||"N/A"})]})})})]}),(0,x.jsxs)(l.A,{className:"mt-4",children:[(0,x.jsx)(t.A,{md:6,className:"text-center",children:(0,x.jsx)(c.A,{className:"card-box card-primary",children:(0,x.jsxs)(c.A.Body,{children:[(0,x.jsx)("h4",{children:e("product_management")}),(0,x.jsx)("p",{children:e("manage_your_products")}),(0,x.jsx)(n.A,{as:o.N_,to:"/maker/products",variant:"primary",children:e("enter_product_list")})]})})}),(0,x.jsx)(t.A,{md:6,className:"text-center",children:(0,x.jsx)(c.A,{className:"card-box card-success",children:(0,x.jsxs)(c.A.Body,{children:[(0,x.jsx)("h4",{children:e("order_management")}),(0,x.jsx)("p",{children:e("all_orders")}),(0,x.jsx)(n.A,{as:o.N_,to:"/maker/orders",variant:"success",children:e("enter_order_list")})]})})})]})]})})}):(0,x.jsx)("div",{className:"alert alert-warning",children:e("not_maker")})}},1072:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),d=r.n(a),l=r(5043),t=r(7852),c=r(579);const n=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...n}=e;const i=(0,t.oU)(r,"row"),o=(0,t.gy)(),m=(0,t.Jm)(),x=`${i}-cols`,f=[];return o.forEach(e=>{const s=n[e];let r;delete n[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==m?`-${e}`:"";null!=r&&f.push(`${x}${a}-${r}`)}),(0,c.jsx)(l,{ref:s,...n,className:d()(a,i,...f)})});n.displayName="Row";const i=n},8602:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),d=r.n(a),l=r(5043),t=r(7852),c=r(579);const n=l.forwardRef((e,s)=>{const[{className:r,...a},{as:l="div",bsPrefix:n,spans:i}]=function(e){let{as:s,bsPrefix:r,className:a,...l}=e;r=(0,t.oU)(r,"col");const c=(0,t.gy)(),n=(0,t.Jm)(),i=[],o=[];return c.forEach(e=>{const s=l[e];let a,d,t;delete l[e],"object"===typeof s&&null!=s?({span:a,offset:d,order:t}=s):a=s;const c=e!==n?`-${e}`:"";a&&i.push(!0===a?`${r}${c}`:`${r}${c}-${a}`),null!=t&&o.push(`order${c}-${t}`),null!=d&&o.push(`offset${c}-${d}`)}),[{...l,className:d()(a,...i,...o)},{as:s,bsPrefix:r,spans:i}]}(e);return(0,c.jsx)(l,{...a,ref:s,className:d()(r,!i.length&&n)})});n.displayName="Col";const i=n},8628:(e,s,r)=>{r.d(s,{A:()=>U});var a=r(8139),d=r.n(a),l=r(5043),t=r(7852),c=r(579);const n=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,t.oU)(a,"card-body"),(0,c.jsx)(l,{ref:s,className:d()(r,a),...n})});n.displayName="CardBody";const i=n,o=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,t.oU)(a,"card-footer"),(0,c.jsx)(l,{ref:s,className:d()(r,a),...n})});o.displayName="CardFooter";const m=o;var x=r(1778);const f=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...i}=e;const o=(0,t.oU)(r,"card-header"),m=(0,l.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,c.jsx)(x.A.Provider,{value:m,children:(0,c.jsx)(n,{ref:s,...i,className:d()(a,o)})})});f.displayName="CardHeader";const h=f,u=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:l,as:n="img",...i}=e;const o=(0,t.oU)(r,"card-img");return(0,c.jsx)(n,{ref:s,className:d()(l?`${o}-${l}`:o,a),...i})});u.displayName="CardImg";const j=u,N=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...n}=e;return a=(0,t.oU)(a,"card-img-overlay"),(0,c.jsx)(l,{ref:s,className:d()(r,a),...n})});N.displayName="CardImgOverlay";const p=N,b=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="a",...n}=e;return a=(0,t.oU)(a,"card-link"),(0,c.jsx)(l,{ref:s,className:d()(r,a),...n})});b.displayName="CardLink";const y=b;var A=r(4488);const v=(0,A.A)("h6"),g=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=v,...n}=e;return a=(0,t.oU)(a,"card-subtitle"),(0,c.jsx)(l,{ref:s,className:d()(r,a),...n})});g.displayName="CardSubtitle";const w=g,_=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="p",...n}=e;return a=(0,t.oU)(a,"card-text"),(0,c.jsx)(l,{ref:s,className:d()(r,a),...n})});_.displayName="CardText";const $=_,P=(0,A.A)("h5"),k=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=P,...n}=e;return a=(0,t.oU)(a,"card-title"),(0,c.jsx)(l,{ref:s,className:d()(r,a),...n})});k.displayName="CardTitle";const C=k,R=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:l,text:n,border:o,body:m=!1,children:x,as:f="div",...h}=e;const u=(0,t.oU)(r,"card");return(0,c.jsx)(f,{ref:s,...h,className:d()(a,u,l&&`bg-${l}`,n&&`text-${n}`,o&&`border-${o}`),children:m?(0,c.jsx)(i,{children:x}):x})});R.displayName="Card";const U=Object.assign(R,{Img:j,Title:C,Subtitle:w,Body:i,Link:y,Text:$,Header:h,Footer:m,ImgOverlay:p})}}]);
//# sourceMappingURL=668.ac5c17c3.chunk.js.map