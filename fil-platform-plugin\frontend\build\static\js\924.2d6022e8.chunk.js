"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[924],{1072:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...c}=e;const i=(0,n.oU)(r,"row"),o=(0,n.gy)(),f=(0,n.Jm)(),x=`${i}-cols`,m=[];return o.forEach(e=>{const s=c[e];let r;delete c[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&m.push(`${x}${a}-${r}`)}),(0,l.jsx)(d,{ref:s,...c,className:t()(a,i,...m)})});c.displayName="Row";const i=c},2924:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),d=r(1072),n=r(8602),l=r(8628),c=r(4196),i=r(4312),o=r(4117),f=r(579);const x=()=>{const{t:e}=(0,o.Bd)(),[s,r]=(0,a.useState)([]),[x,m]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,i.b)();if(!e)return;m(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void m(!1);const{data:a,error:t}=await e.from("miners").select("\n                    id,\n                    category,\n                    filecoin_miner_id,\n                    sector_size,\n                    effective_until,\n                    created_at,\n                    updated_at,\n                    facilities (\n                        name\n                    )\n                ").order("created_at",{ascending:!1});t?console.error("Error fetching miners:",t):r(a),m(!1)})()},[]),x?(0,f.jsx)("div",{children:e("loading_miners")}):(0,f.jsxs)(t.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("all_miners")}),(0,f.jsx)(d.A,{children:(0,f.jsx)(n.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsx)(l.A.Body,{children:(0,f.jsxs)(c.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:"ID"}),(0,f.jsx)("th",{children:e("category")}),(0,f.jsx)("th",{children:e("facility")}),(0,f.jsx)("th",{children:e("miner_id")}),(0,f.jsx)("th",{children:e("effective_until")}),(0,f.jsx)("th",{children:e("created_at")}),(0,f.jsx)("th",{children:e("updated_at")}),(0,f.jsx)("th",{children:e("actions")})]})}),(0,f.jsx)("tbody",{children:0===s.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_miners_available")})}):s.map(e=>{var s;return(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:e.category}),(0,f.jsx)("td",{children:(null===(s=e.facilities)||void 0===s?void 0:s.name)||"-"}),(0,f.jsx)("td",{children:e.filecoin_miner_id}),(0,f.jsx)("td",{children:e.sector_size}),(0,f.jsx)("td",{children:e.effective_until}),(0,f.jsx)("td",{children:new Date(e.created_at).toLocaleString()}),(0,f.jsx)("td",{children:new Date(e.updated_at).toLocaleString()}),(0,f.jsx)("td",{children:"Edit/Delete"})]},e.id)})})]})})})})})]})}},4196:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:c,borderless:i,hover:o,size:f,variant:x,responsive:m,...h}=e;const u=(0,n.oU)(r,"table"),j=t()(a,u,x&&`${u}-${x}`,f&&`${u}-${f}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,c&&`${u}-bordered`,i&&`${u}-borderless`,o&&`${u}-hover`),N=(0,l.jsx)("table",{...h,className:j,ref:s});if(m){let e=`${u}-responsive`;return"string"===typeof m&&(e=`${e}-${m}`),(0,l.jsx)("div",{className:e,children:N})}return N});c.displayName="Table";const i=c},8602:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:c,spans:i}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,n.oU)(r,"col");const l=(0,n.gy)(),c=(0,n.Jm)(),i=[],o=[];return l.forEach(e=>{const s=d[e];let a,t,n;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:n}=s):a=s;const l=e!==c?`-${e}`:"";a&&i.push(!0===a?`${r}${l}`:`${r}${l}-${a}`),null!=n&&o.push(`order${l}-${n}`),null!=t&&o.push(`offset${l}-${t}`)}),[{...d,className:t()(a,...i,...o)},{as:s,bsPrefix:r,spans:i}]}(e);return(0,l.jsx)(d,{...a,ref:s,className:t()(r,!i.length&&c)})});c.displayName="Col";const i=c},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),n=r(7852),l=r(579);const c=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-body"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});c.displayName="CardBody";const i=c,o=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-footer"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});o.displayName="CardFooter";const f=o;var x=r(1778);const m=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:c="div",...i}=e;const o=(0,n.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,l.jsx)(x.A.Provider,{value:f,children:(0,l.jsx)(c,{ref:s,...i,className:t()(a,o)})})});m.displayName="CardHeader";const h=m,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:c="img",...i}=e;const o=(0,n.oU)(r,"card-img");return(0,l.jsx)(c,{ref:s,className:t()(d?`${o}-${d}`:o,a),...i})});u.displayName="CardImg";const j=u,N=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...c}=e;return a=(0,n.oU)(a,"card-img-overlay"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});N.displayName="CardImgOverlay";const b=N,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...c}=e;return a=(0,n.oU)(a,"card-link"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});p.displayName="CardLink";const v=p;var y=r(4488);const $=(0,y.A)("h6"),g=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=$,...c}=e;return a=(0,n.oU)(a,"card-subtitle"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});g.displayName="CardSubtitle";const w=g,_=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...c}=e;return a=(0,n.oU)(a,"card-text"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});_.displayName="CardText";const P=_,R=(0,y.A)("h5"),U=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=R,...c}=e;return a=(0,n.oU)(a,"card-title"),(0,l.jsx)(d,{ref:s,className:t()(r,a),...c})});U.displayName="CardTitle";const A=U,C=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:c,border:o,body:f=!1,children:x,as:m="div",...h}=e;const u=(0,n.oU)(r,"card");return(0,l.jsx)(m,{ref:s,...h,className:t()(a,u,d&&`bg-${d}`,c&&`text-${c}`,o&&`border-${o}`),children:f?(0,l.jsx)(i,{children:x}):x})});C.displayName="Card";const k=Object.assign(C,{Img:j,Title:A,Subtitle:w,Body:i,Link:v,Text:P,Header:h,Footer:f,ImgOverlay:b})}}]);
//# sourceMappingURL=924.2d6022e8.chunk.js.map