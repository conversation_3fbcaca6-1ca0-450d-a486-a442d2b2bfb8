{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MinerEarnings=()=>{const{t}=useTranslation();const[earnings,setEarnings]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchEarnings=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch miner daily earnings with miner information\nconst{data,error}=await supabase.from('miner_daily_earnings').select(`\n                    miner_id,\n                    earn_date,\n                    cumulative_reward,\n                    daily_reward,\n                    blocks_won,\n                    created_at,\n                    miners (\n                        filecoin_miner_id,\n                        category,\n                        facilities (\n                            name\n                        )\n                    )\n                `).order('earn_date',{ascending:false});if(error){console.error('Error fetching miner earnings:',error);}else{setEarnings(data);}setLoading(false);};fetchEarnings();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_earnings')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('miner_earnings')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('miner_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('facility')}),/*#__PURE__*/_jsx(\"th\",{children:t('earn_date')}),/*#__PURE__*/_jsx(\"th\",{children:t('daily_reward')}),/*#__PURE__*/_jsx(\"th\",{children:t('cumulative_reward')}),/*#__PURE__*/_jsx(\"th\",{children:t('blocks_won')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:earnings.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"7\",className:\"text-center\",children:t('no_earnings_available')})}):earnings.map((earning,index)=>{var _earning$miners,_earning$miners2,_earning$miners2$faci;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:((_earning$miners=earning.miners)===null||_earning$miners===void 0?void 0:_earning$miners.filecoin_miner_id)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:((_earning$miners2=earning.miners)===null||_earning$miners2===void 0?void 0:(_earning$miners2$faci=_earning$miners2.facilities)===null||_earning$miners2$faci===void 0?void 0:_earning$miners2$faci.name)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:new Date(earning.earn_date).toLocaleDateString()}),/*#__PURE__*/_jsxs(\"td\",{children:[earning.daily_reward?Number(earning.daily_reward).toFixed(6):'0',\" FIL\"]}),/*#__PURE__*/_jsxs(\"td\",{children:[earning.cumulative_reward?Number(earning.cumulative_reward).toFixed(6):'0',\" FIL\"]}),/*#__PURE__*/_jsx(\"td\",{children:earning.blocks_won||0}),/*#__PURE__*/_jsx(\"td\",{children:new Date(earning.created_at).toLocaleString()})]},`${earning.miner_id}-${earning.earn_date}`);})})]})})})})})]});};export default MinerEarnings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "t", "earnings", "setEarnings", "loading", "setLoading", "fetchEarnings", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "earning", "index", "_earning$miners", "_earning$miners2", "_earning$miners2$faci", "miners", "filecoin_miner_id", "facilities", "name", "Date", "earn_date", "toLocaleDateString", "daily_reward", "Number", "toFixed", "cumulative_reward", "blocks_won", "created_at", "toLocaleString", "miner_id"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/MinerEarnings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst MinerEarnings = () => {\n    const { t } = useTranslation();\n    const [earnings, setEarnings] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        const fetchEarnings = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n\n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n\n            // Fetch miner daily earnings with miner information\n            const { data, error } = await supabase\n                .from('miner_daily_earnings')\n                .select(`\n                    miner_id,\n                    earn_date,\n                    cumulative_reward,\n                    daily_reward,\n                    blocks_won,\n                    created_at,\n                    miners (\n                        filecoin_miner_id,\n                        category,\n                        facilities (\n                            name\n                        )\n                    )\n                `)\n                .order('earn_date', { ascending: false });\n\n            if (error) {\n                console.error('Error fetching miner earnings:', error);\n            } else {\n                setEarnings(data);\n            }\n            setLoading(false);\n        };\n\n        fetchEarnings();\n    }, []);\n\n    if (loading) {\n        return <div>{t('loading_earnings')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('miner_earnings')}</h2>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Table striped bordered hover responsive>\n                                <thead>\n                                    <tr>\n                                        <th>{t('miner_id')}</th>\n                                        <th>{t('facility')}</th>\n                                        <th>{t('earn_date')}</th>\n                                        <th>{t('daily_reward')}</th>\n                                        <th>{t('cumulative_reward')}</th>\n                                        <th>{t('blocks_won')}</th>\n                                        <th>{t('created_at')}</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {earnings.length === 0 ? (\n                                        <tr>\n                                            <td colSpan=\"7\" className=\"text-center\">{t('no_earnings_available')}</td>\n                                        </tr>\n                                    ) : (\n                                        earnings.map((earning, index) => (\n                                            <tr key={`${earning.miner_id}-${earning.earn_date}`}>\n                                                <td>{earning.miners?.filecoin_miner_id || '-'}</td>\n                                                <td>{earning.miners?.facilities?.name || '-'}</td>\n                                                <td>{new Date(earning.earn_date).toLocaleDateString()}</td>\n                                                <td>{earning.daily_reward ? Number(earning.daily_reward).toFixed(6) : '0'} FIL</td>\n                                                <td>{earning.cumulative_reward ? Number(earning.cumulative_reward).toFixed(6) : '0'} FIL</td>\n                                                <td>{earning.blocks_won || 0}</td>\n                                                <td>{new Date(earning.created_at).toLocaleString()}</td>\n                                            </tr>\n                                        ))\n                                    )}\n                                </tbody>\n                            </Table>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MinerEarnings;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE7C,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CAC1D,CAAC,IAAM,CACHT,WAAW,CAACK,IAAI,CAAC,CACrB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,aAAa,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAqB,QAAA,CAAMjB,CAAC,CAAC,kBAAkB,CAAC,CAAM,CAAC,CAC7C,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAA8B,QAAA,eACNrB,IAAA,OAAIsB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEjB,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC/CJ,IAAA,CAACR,GAAG,EAAA6B,QAAA,cACArB,IAAA,CAACP,GAAG,EAAA4B,QAAA,cACArB,IAAA,CAACN,IAAI,EAAA2B,QAAA,cACDrB,IAAA,CAACN,IAAI,CAAC6B,IAAI,EAAAF,QAAA,cACNnB,KAAA,CAACP,KAAK,EAAC6B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCrB,IAAA,UAAAqB,QAAA,cACInB,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,cAAc,CAAC,CAAK,CAAC,cAC5BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,EAC1B,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAqB,QAAA,CACKhB,QAAQ,CAACuB,MAAM,GAAK,CAAC,cAClB5B,IAAA,OAAAqB,QAAA,cACIrB,IAAA,OAAI6B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEjB,CAAC,CAAC,uBAAuB,CAAC,CAAK,CAAC,CACzE,CAAC,CAELC,QAAQ,CAACyB,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,QAAAC,eAAA,CAAAC,gBAAA,CAAAC,qBAAA,oBACxBjC,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAK,EAAAY,eAAA,CAAAF,OAAO,CAACK,MAAM,UAAAH,eAAA,iBAAdA,eAAA,CAAgBI,iBAAiB,GAAI,GAAG,CAAK,CAAC,cACnDrC,IAAA,OAAAqB,QAAA,CAAK,EAAAa,gBAAA,CAAAH,OAAO,CAACK,MAAM,UAAAF,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBI,UAAU,UAAAH,qBAAA,iBAA1BA,qBAAA,CAA4BI,IAAI,GAAI,GAAG,CAAK,CAAC,cAClDvC,IAAA,OAAAqB,QAAA,CAAK,GAAI,CAAAmB,IAAI,CAACT,OAAO,CAACU,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAK,CAAC,cAC3DxC,KAAA,OAAAmB,QAAA,EAAKU,OAAO,CAACY,YAAY,CAAGC,MAAM,CAACb,OAAO,CAACY,YAAY,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,MAAI,EAAI,CAAC,cACnF3C,KAAA,OAAAmB,QAAA,EAAKU,OAAO,CAACe,iBAAiB,CAAGF,MAAM,CAACb,OAAO,CAACe,iBAAiB,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CAAC,MAAI,EAAI,CAAC,cAC7F7C,IAAA,OAAAqB,QAAA,CAAKU,OAAO,CAACgB,UAAU,EAAI,CAAC,CAAK,CAAC,cAClC/C,IAAA,OAAAqB,QAAA,CAAK,GAAI,CAAAmB,IAAI,CAACT,OAAO,CAACiB,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,GAPnD,GAAGlB,OAAO,CAACmB,QAAQ,IAAInB,OAAO,CAACU,SAAS,EAQ7C,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAtC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}