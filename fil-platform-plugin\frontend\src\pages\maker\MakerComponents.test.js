import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../i18n';
import MinerEarnings from './MinerEarnings';
import MinerSnapshots from './MinerSnapshots';
import Transactions from './Transactions';

// Mock the supabaseClient
jest.mock('../../supabaseClient', () => ({
  getSupabase: jest.fn(() => null)
}));

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </BrowserRouter>
  );
};

describe('Maker Components', () => {
  test('MinerEarnings renders without crashing', () => {
    renderWithProviders(<MinerEarnings />);
    // Should show loading state when supabase is not available
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  test('MinerSnapshots renders without crashing', () => {
    renderWithProviders(<MinerSnapshots />);
    // Should show loading state when supabase is not available
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  test('Transactions renders without crashing', () => {
    renderWithProviders(<Transactions />);
    // Should show loading state when supabase is not available
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });
});
