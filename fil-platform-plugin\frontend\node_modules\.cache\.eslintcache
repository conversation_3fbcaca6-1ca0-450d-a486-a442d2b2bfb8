[{"D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js": "1", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js": "2", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js": "3", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js": "4", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js": "6", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js": "7", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js": "8", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js": "9", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js": "10", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js": "11", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js": "12", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js": "13", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js": "14", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js": "15", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js": "16", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js": "17", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js": "18", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js": "19", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js": "20", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js": "21", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js": "22", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js": "23", "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js": "24"}, {"size": 408, "mtime": 1751951658003, "results": "25", "hashOfConfig": "26"}, {"size": 10409, "mtime": 1752111961643, "results": "27", "hashOfConfig": "26"}, {"size": 26349, "mtime": 1751967598440, "results": "28", "hashOfConfig": "26"}, {"size": 1212, "mtime": 1751873051207, "results": "29", "hashOfConfig": "26"}, {"size": 3250, "mtime": 1751953679420, "results": "30", "hashOfConfig": "26"}, {"size": 4791, "mtime": 1751960448046, "results": "31", "hashOfConfig": "26"}, {"size": 4036, "mtime": 1752109725670, "results": "32", "hashOfConfig": "26"}, {"size": 4610, "mtime": 1751946228349, "results": "33", "hashOfConfig": "26"}, {"size": 5273, "mtime": 1751960463052, "results": "34", "hashOfConfig": "26"}, {"size": 8598, "mtime": 1751939997191, "results": "35", "hashOfConfig": "26"}, {"size": 4230, "mtime": 1751940026705, "results": "36", "hashOfConfig": "26"}, {"size": 1735, "mtime": 1751940008151, "results": "37", "hashOfConfig": "26"}, {"size": 4711, "mtime": 1751946191614, "results": "38", "hashOfConfig": "26"}, {"size": 4495, "mtime": 1751940037703, "results": "39", "hashOfConfig": "26"}, {"size": 3655, "mtime": 1751948557098, "results": "40", "hashOfConfig": "26"}, {"size": 4863, "mtime": 1751950041198, "results": "41", "hashOfConfig": "26"}, {"size": 3929, "mtime": 1751946465633, "results": "42", "hashOfConfig": "26"}, {"size": 4027, "mtime": 1751945104895, "results": "43", "hashOfConfig": "26"}, {"size": 3679, "mtime": 1751944188070, "results": "44", "hashOfConfig": "26"}, {"size": 4387, "mtime": 1752112003947, "results": "45", "hashOfConfig": "26"}, {"size": 3678, "mtime": 1751967553714, "results": "46", "hashOfConfig": "26"}, {"size": 4650, "mtime": 1752111918233, "results": "47", "hashOfConfig": "26"}, {"size": 5979, "mtime": 1752111934504, "results": "48", "hashOfConfig": "26"}, {"size": 4355, "mtime": 1752111904775, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ji7irk", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\New_System\\fil-platform-plugin\\frontend\\src\\index.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\App.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\i18n.js", ["122", "123", "124", "125", "126", "127"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\supabaseClient.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerOrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\RecommendPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\KycPage.js", ["128"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyGainsPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\MyAccountPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\WalletPage.js", ["129"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\OrderListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\ProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\customer\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\Dashboard.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentProductListPage.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\agent\\AgentMemberList.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerMiners.js", ["130"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MakerFacilities.js", ["131"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerSnapshots.js", ["132"], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\Transactions.js", [], [], "D:\\New_System\\fil-platform-plugin\\frontend\\src\\pages\\maker\\MinerEarnings.js", ["133"], [], {"ruleId": "134", "severity": 1, "message": "135", "line": 138, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 138, "endColumn": 18}, {"ruleId": "134", "severity": 1, "message": "138", "line": 140, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 140, "endColumn": 23}, {"ruleId": "134", "severity": 1, "message": "135", "line": 336, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 336, "endColumn": 18}, {"ruleId": "134", "severity": 1, "message": "138", "line": 338, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 338, "endColumn": 23}, {"ruleId": "134", "severity": 1, "message": "135", "line": 534, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 534, "endColumn": 18}, {"ruleId": "134", "severity": 1, "message": "138", "line": 536, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 536, "endColumn": 23}, {"ruleId": "139", "severity": 1, "message": "140", "line": 49, "column": 8, "nodeType": "141", "endLine": 49, "endColumn": 10, "suggestions": "142"}, {"ruleId": "143", "severity": 1, "message": "144", "line": 3, "column": 44, "nodeType": "145", "messageId": "146", "endLine": 3, "endColumn": 50}, {"ruleId": "143", "severity": 1, "message": "147", "line": 2, "column": 44, "nodeType": "145", "messageId": "146", "endLine": 2, "endColumn": 49}, {"ruleId": "143", "severity": 1, "message": "147", "line": 2, "column": 44, "nodeType": "145", "messageId": "146", "endLine": 2, "endColumn": 49}, {"ruleId": "143", "severity": 1, "message": "147", "line": 2, "column": 44, "nodeType": "145", "messageId": "146", "endLine": 2, "endColumn": 49}, {"ruleId": "143", "severity": 1, "message": "147", "line": 2, "column": 44, "nodeType": "145", "messageId": "146", "endLine": 2, "endColumn": 49}, "no-dupe-keys", "Duplicate key 'no_assets'.", "ObjectExpression", "unexpected", "Duplicate key 'my_invite_code'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["148"], "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'Badge' is defined but never used.", {"desc": "149", "fix": "150"}, "Update the dependencies array to be: [t]", {"range": "151", "text": "152"}, [1977, 1979], "[t]"]