import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MakerMiners = () => {
    const { t } = useTranslation();
    const [miners, setMiners] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchMiners = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch miners associated with products from this maker
            const { data, error } = await supabase
                .from('miners')
                .select(`
                    id,
                    category,
                    filecoin_miner_id,
                    sector_size,
                    effective_until,
                    created_at,
                    updated_at,
                    facilities (
                        name
                    )
                `)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching miners:', error);
            } else {
                setMiners(data);
            }
            setLoading(false);
        };

        fetchMiners();
    }, []);

    if (loading) {
        return <div>{t('loading_miners')}</div>;
    }

    return (
        <Container>
                <h2 className="mb-4">{t('all_miners')}</h2>
                <Row>
                    <Col>
                        <Card>
                            <Card.Body>
                                <Table striped bordered hover responsive>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>{t('category')}</th>
                                            <th>{t('facility')}</th>
                                            <th>{t('miner_id')}</th>
                                            <th>{t('effective_until')}</th>
                                            <th>{t('created_at')}</th>
                                            <th>{t('updated_at')}</th>
                                            <th>{t('actions')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {miners.length === 0 ? (
                                            <tr>
                                                <td colSpan="9" className="text-center">{t('no_miners_available')}</td>
                                            </tr>
                                        ) : (
                                            miners.map(miner => (
                                                <tr key={miner.id}>
                                                    <td>{miner.category}</td>
                                                    <td>{miner.facilities?.name || '-'}</td>
                                                    <td>{miner.filecoin_miner_id}</td>
                                                    <td>{miner.sector_size}</td>
                                                    <td>{miner.effective_until}</td>
                                                    <td>{new Date(miner.created_at).toLocaleString()}</td>
                                                    <td>{new Date(miner.updated_at).toLocaleString()}</td>
                                                    <td>Edit/Delete</td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </Table>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
        </Container>
    );
};

export default MakerMiners;
