{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Table,Badge}from'react-bootstrap';import{getSupabase}from'../../supabaseClient';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MakerMiners=()=>{const{t}=useTranslation();const[miners,setMiners]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchMiners=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}// Fetch miners associated with products from this maker\nconst{data,error}=await supabase.from('miners').select(`\n                    id,\n                    category,\n                    filecoin_miner_id,\n                    sector_size,\n                    effective_until,\n                    created_at,\n                    updated_at,\n                    facilities (\n                        name\n                    )\n                `).order('created_at',{ascending:false});if(error){console.error('Error fetching miners:',error);}else{setMiners(data);}setLoading(false);};fetchMiners();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_miners')});}return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('all_miners')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:t('category')}),/*#__PURE__*/_jsx(\"th\",{children:t('facility')}),/*#__PURE__*/_jsx(\"th\",{children:t('miner_id')}),/*#__PURE__*/_jsx(\"th\",{children:t('effective_until')}),/*#__PURE__*/_jsx(\"th\",{children:t('created_at')}),/*#__PURE__*/_jsx(\"th\",{children:t('updated_at')}),/*#__PURE__*/_jsx(\"th\",{children:t('actions')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:miners.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"9\",className:\"text-center\",children:t('no_miners_available')})}):miners.map(miner=>{var _miner$facilities;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:miner.category}),/*#__PURE__*/_jsx(\"td\",{children:((_miner$facilities=miner.facilities)===null||_miner$facilities===void 0?void 0:_miner$facilities.name)||'-'}),/*#__PURE__*/_jsx(\"td\",{children:miner.filecoin_miner_id}),/*#__PURE__*/_jsx(\"td\",{children:miner.sector_size}),/*#__PURE__*/_jsx(\"td\",{children:miner.effective_until}),/*#__PURE__*/_jsx(\"td\",{children:new Date(miner.created_at).toLocaleString()}),/*#__PURE__*/_jsx(\"td\",{children:new Date(miner.updated_at).toLocaleString()}),/*#__PURE__*/_jsx(\"td\",{children:\"Edit/Delete\"})]},miner.id);})})]})})})})})]});};export default MakerMiners;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "Badge", "getSupabase", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "MakerMiners", "t", "miners", "setMiners", "loading", "setLoading", "fetchMiners", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "children", "className", "Body", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "miner", "_miner$facilities", "category", "facilities", "name", "filecoin_miner_id", "sector_size", "effective_until", "Date", "created_at", "toLocaleString", "updated_at", "id"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/maker/MakerMiners.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\r\nimport { getSupabase } from '../../supabaseClient';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst MakerMiners = () => {\r\n    const { t } = useTranslation();\r\n    const [miners, setMiners] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    useEffect(() => {\r\n        const fetchMiners = async () => {\r\n            const supabase = getSupabase();\r\n            if (!supabase) return;\r\n\r\n            setLoading(true);\r\n            const { data: { user } } = await supabase.auth.getUser();\r\n\r\n            if (!user) {\r\n                setLoading(false);\r\n                return; // User not logged in\r\n            }\r\n\r\n            // Fetch miners associated with products from this maker\r\n            const { data, error } = await supabase\r\n                .from('miners')\r\n                .select(`\r\n                    id,\r\n                    category,\r\n                    filecoin_miner_id,\r\n                    sector_size,\r\n                    effective_until,\r\n                    created_at,\r\n                    updated_at,\r\n                    facilities (\r\n                        name\r\n                    )\r\n                `)\r\n                .order('created_at', { ascending: false });\r\n\r\n            if (error) {\r\n                console.error('Error fetching miners:', error);\r\n            } else {\r\n                setMiners(data);\r\n            }\r\n            setLoading(false);\r\n        };\r\n\r\n        fetchMiners();\r\n    }, []);\r\n\r\n    if (loading) {\r\n        return <div>{t('loading_miners')}</div>;\r\n    }\r\n\r\n    return (\r\n        <Container>\r\n                <h2 className=\"mb-4\">{t('all_miners')}</h2>\r\n                <Row>\r\n                    <Col>\r\n                        <Card>\r\n                            <Card.Body>\r\n                                <Table striped bordered hover responsive>\r\n                                    <thead>\r\n                                        <tr>\r\n                                            <th>ID</th>\r\n                                            <th>{t('category')}</th>\r\n                                            <th>{t('facility')}</th>\r\n                                            <th>{t('miner_id')}</th>\r\n                                            <th>{t('effective_until')}</th>\r\n                                            <th>{t('created_at')}</th>\r\n                                            <th>{t('updated_at')}</th>\r\n                                            <th>{t('actions')}</th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody>\r\n                                        {miners.length === 0 ? (\r\n                                            <tr>\r\n                                                <td colSpan=\"9\" className=\"text-center\">{t('no_miners_available')}</td>\r\n                                            </tr>\r\n                                        ) : (\r\n                                            miners.map(miner => (\r\n                                                <tr key={miner.id}>\r\n                                                    <td>{miner.category}</td>\r\n                                                    <td>{miner.facilities?.name || '-'}</td>\r\n                                                    <td>{miner.filecoin_miner_id}</td>\r\n                                                    <td>{miner.sector_size}</td>\r\n                                                    <td>{miner.effective_until}</td>\r\n                                                    <td>{new Date(miner.created_at).toLocaleString()}</td>\r\n                                                    <td>{new Date(miner.updated_at).toLocaleString()}</td>\r\n                                                    <td>Edit/Delete</td>\r\n                                                </tr>\r\n                                            ))\r\n                                        )}\r\n                                    </tbody>\r\n                                </Table>\r\n                            </Card.Body>\r\n                        </Card>\r\n                    </Col>\r\n                </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default MakerMiners;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACzE,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACO,MAAM,CAAEC,SAAS,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAmB,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC5B,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACa,QAAQ,CAAE,OAEfF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEG,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPJ,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA;AACA,KAAM,CAAEG,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CACDC,KAAK,CAAC,YAAY,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CAE9C,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAClD,CAAC,IAAM,CACHT,SAAS,CAACK,IAAI,CAAC,CACnB,CACAH,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDC,WAAW,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIF,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAqB,QAAA,CAAMjB,CAAC,CAAC,gBAAgB,CAAC,CAAM,CAAC,CAC3C,CAEA,mBACIF,KAAA,CAACX,SAAS,EAAA8B,QAAA,eACFrB,IAAA,OAAIsB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEjB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC3CJ,IAAA,CAACR,GAAG,EAAA6B,QAAA,cACArB,IAAA,CAACP,GAAG,EAAA4B,QAAA,cACArB,IAAA,CAACN,IAAI,EAAA2B,QAAA,cACDrB,IAAA,CAACN,IAAI,CAAC6B,IAAI,EAAAF,QAAA,cACNnB,KAAA,CAACP,KAAK,EAAC6B,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAN,QAAA,eACpCrB,IAAA,UAAAqB,QAAA,cACInB,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAI,IAAE,CAAI,CAAC,cACXrB,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,iBAAiB,CAAC,CAAK,CAAC,cAC/BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAC1BJ,IAAA,OAAAqB,QAAA,CAAKjB,CAAC,CAAC,SAAS,CAAC,CAAK,CAAC,EACvB,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAqB,QAAA,CACKhB,MAAM,CAACuB,MAAM,GAAK,CAAC,cAChB5B,IAAA,OAAAqB,QAAA,cACIrB,IAAA,OAAI6B,OAAO,CAAC,GAAG,CAACP,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAEjB,CAAC,CAAC,qBAAqB,CAAC,CAAK,CAAC,CACvE,CAAC,CAELC,MAAM,CAACyB,GAAG,CAACC,KAAK,OAAAC,iBAAA,oBACZ9B,KAAA,OAAAmB,QAAA,eACIrB,IAAA,OAAAqB,QAAA,CAAKU,KAAK,CAACE,QAAQ,CAAK,CAAC,cACzBjC,IAAA,OAAAqB,QAAA,CAAK,EAAAW,iBAAA,CAAAD,KAAK,CAACG,UAAU,UAAAF,iBAAA,iBAAhBA,iBAAA,CAAkBG,IAAI,GAAI,GAAG,CAAK,CAAC,cACxCnC,IAAA,OAAAqB,QAAA,CAAKU,KAAK,CAACK,iBAAiB,CAAK,CAAC,cAClCpC,IAAA,OAAAqB,QAAA,CAAKU,KAAK,CAACM,WAAW,CAAK,CAAC,cAC5BrC,IAAA,OAAAqB,QAAA,CAAKU,KAAK,CAACO,eAAe,CAAK,CAAC,cAChCtC,IAAA,OAAAqB,QAAA,CAAK,GAAI,CAAAkB,IAAI,CAACR,KAAK,CAACS,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAK,CAAC,cACtDzC,IAAA,OAAAqB,QAAA,CAAK,GAAI,CAAAkB,IAAI,CAACR,KAAK,CAACW,UAAU,CAAC,CAACD,cAAc,CAAC,CAAC,CAAK,CAAC,cACtDzC,IAAA,OAAAqB,QAAA,CAAI,aAAW,CAAI,CAAC,GARfU,KAAK,CAACY,EASX,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACD,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACH,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAxC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}