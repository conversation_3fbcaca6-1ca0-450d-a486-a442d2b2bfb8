{"version": 3, "file": "static/js/924.2d6022e8.chunk.js", "mappings": "wKAMA,MAAMA,EAAmBC,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,OACjDO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCG,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWI,KAAsBO,OAG3DhB,EAAIyB,YAAc,MAClB,S,4IClCA,MAmGA,EAnGoBC,KAChB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAC9BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GA2CvC,OAzCAG,EAAAA,EAAAA,WAAU,KACcC,WAChB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEfH,GAAW,GACX,MAAQK,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAE/C,IAAKF,EAED,YADAN,GAAW,GAKf,MAAM,KAAEK,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,UACLC,OAAO,uVAYPC,MAAM,aAAc,CAAEC,WAAW,IAElCJ,EACAK,QAAQL,MAAM,yBAA0BA,GAExCZ,EAAUQ,GAEdL,GAAW,IAGfe,IACD,IAEChB,GACOT,EAAAA,EAAAA,KAAA,OAAA0B,SAAMtB,EAAE,qBAIfuB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAAAF,SAAA,EACF1B,EAAAA,EAAAA,KAAA,MAAIlB,UAAU,OAAM4C,SAAEtB,EAAE,iBACxBJ,EAAAA,EAAAA,KAACvB,EAAAA,EAAG,CAAAiD,UACA1B,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAAAH,UACA1B,EAAAA,EAAAA,KAAC8B,EAAAA,EAAI,CAAAJ,UACD1B,EAAAA,EAAAA,KAAC8B,EAAAA,EAAKC,KAAI,CAAAL,UACNC,EAAAA,EAAAA,MAACK,EAAAA,EAAK,CAACC,SAAO,EAACC,UAAQ,EAACC,OAAK,EAACC,YAAU,EAAAV,SAAA,EACpC1B,EAAAA,EAAAA,KAAA,SAAA0B,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI1B,EAAAA,EAAAA,KAAA,MAAA0B,SAAI,QACJ1B,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,eACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,sBACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,iBACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,iBACPJ,EAAAA,EAAAA,KAAA,MAAA0B,SAAKtB,EAAE,mBAGfJ,EAAAA,EAAAA,KAAA,SAAA0B,SACuB,IAAlBpB,EAAO+B,QACJrC,EAAAA,EAAAA,KAAA,MAAA0B,UACI1B,EAAAA,EAAAA,KAAA,MAAIsC,QAAQ,IAAIxD,UAAU,cAAa4C,SAAEtB,EAAE,2BAG/CE,EAAOiC,IAAIC,IAAK,IAAAC,EAAA,OACZd,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACI1B,EAAAA,EAAAA,KAAA,MAAA0B,SAAKc,EAAME,YACX1C,EAAAA,EAAAA,KAAA,MAAA0B,UAAqB,QAAhBe,EAAAD,EAAMG,kBAAU,IAAAF,OAAA,EAAhBA,EAAkBG,OAAQ,OAC/B5C,EAAAA,EAAAA,KAAA,MAAA0B,SAAKc,EAAMK,qBACX7C,EAAAA,EAAAA,KAAA,MAAA0B,SAAKc,EAAMM,eACX9C,EAAAA,EAAAA,KAAA,MAAA0B,SAAKc,EAAMO,mBACX/C,EAAAA,EAAAA,KAAA,MAAA0B,SAAK,IAAIsB,KAAKR,EAAMS,YAAYC,oBAChClD,EAAAA,EAAAA,KAAA,MAAA0B,SAAK,IAAIsB,KAAKR,EAAMW,YAAYD,oBAChClD,EAAAA,EAAAA,KAAA,MAAA0B,SAAI,kBARCc,EAAMY,sB,sFC5E/D,MAAMpB,EAAqBtD,EAAAA,WAAiB,CAAAC,EAWzCC,KAAQ,IAXkC,SAC3CC,EAAQ,UACRC,EAAS,QACTmD,EAAO,SACPC,EAAQ,WACRmB,EAAU,MACVlB,EAAK,KACLmB,EAAI,QACJC,EAAO,WACPnB,KACGnD,GACJN,EACC,MAAMO,GAAoBC,EAAAA,EAAAA,IAAmBN,EAAU,SACjDY,EAAUQ,IAAWnB,EAAWI,EAAmBqE,GAAW,GAAGrE,KAAqBqE,IAAWD,GAAQ,GAAGpE,KAAqBoE,IAAQrB,GAAW,GAAG/C,KAAwC,kBAAZ+C,EAAuB,WAAWA,IAAY,YAAaC,GAAY,GAAGhD,aAA8BmE,GAAc,GAAGnE,eAAgCiD,GAAS,GAAGjD,WACxVsE,GAAqBxD,EAAAA,EAAAA,KAAK,QAAS,IACpCf,EACHH,UAAWW,EACXb,IAAKA,IAEP,GAAIwD,EAAY,CACd,IAAIqB,EAAkB,GAAGvE,eAIzB,MAH0B,kBAAfkD,IACTqB,EAAkB,GAAGA,KAAmBrB,MAEtBpC,EAAAA,EAAAA,KAAK,MAAO,CAC9BlB,UAAW2E,EACX/B,SAAU8B,GAEd,CACA,OAAOA,IAETxB,EAAM9B,YAAc,QACpB,S,sFCQA,MAAM2B,EAAmBnD,EAAAA,WAEzB,CAACO,EAAOL,KACN,OAAO,UACLE,KACG4E,IAEH3E,GAAIC,EAAY,MAAK,SACrBH,EAAQ,MACR8E,IAjDG,SAAehF,GAKnB,IALoB,GACrBI,EAAE,SACFF,EAAQ,UACRC,KACGG,GACJN,EACCE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,OACxC,MAAMO,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBoE,EAAQ,GACRlE,EAAU,GAqBhB,OApBAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIiE,EACAC,EACAvC,SAHGrC,EAAMU,GAIY,kBAAdC,GAAuC,MAAbA,IAEjCgE,OACAC,SACAvC,SACE1B,GAEJgE,EAAOhE,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GACxDiE,GAAMD,EAAM5D,MAAc,IAAT6D,EAAgB,GAAG/E,IAAWiB,IAAU,GAAGjB,IAAWiB,KAAS8D,KACvE,MAATtC,GAAe7B,EAAQM,KAAK,QAAQD,KAASwB,KACnC,MAAVuC,GAAgBpE,EAAQM,KAAK,SAASD,KAAS+D,OAE9C,CAAC,IACH5E,EACHH,UAAWmB,IAAWnB,KAAc6E,KAAUlE,IAC7C,CACDV,KACAF,WACA8E,SAEJ,CAWOG,CAAO7E,GACZ,OAAoBe,EAAAA,EAAAA,KAAKhB,EAAW,IAC/B0E,EACH9E,IAAKA,EACLE,UAAWmB,IAAWnB,GAAY6E,EAAMtB,QAAUxD,OAGtDgD,EAAI3B,YAAc,MAClB,S,sFC1DA,MAAM6D,EAAwBrF,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP8E,EAAS7D,YAAc,WACvB,UCdM8D,EAA0BtF,EAAAA,WAAiB,CAAAC,EAK9CC,KAAQ,IALuC,UAChDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,gBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP+E,EAAW9D,YAAc,aACzB,U,cCZA,MAAM+D,EAA0BvF,EAAAA,WAAiB,CAAAC,EAM9CC,KAAQ,IANuC,SAChDC,EAAQ,UACRC,EAEAC,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMuF,GAAS/E,EAAAA,EAAAA,IAAmBN,EAAU,eACtCsF,GAAeC,EAAAA,EAAAA,SAAQ,KAAM,CACjCC,mBAAoBH,IAClB,CAACA,IACL,OAAoBlE,EAAAA,EAAAA,KAAKsE,EAAAA,EAAkBC,SAAU,CACnDC,MAAOL,EACPzC,UAAuB1B,EAAAA,EAAAA,KAAKhB,EAAW,CACrCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWoF,SAIvCD,EAAW/D,YAAc,aACzB,UCvBMuE,EAAuB/F,EAAAA,WAE7B,CAAAC,EAMGC,KAAQ,IANV,SACCC,EAAQ,UACRC,EAAS,QACTyE,EACAxE,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMuF,GAAS/E,EAAAA,EAAAA,IAAmBN,EAAU,YAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWsD,EAAU,GAAGW,KAAUX,IAAYW,EAAQpF,MAC9DG,MAGPwF,EAAQvE,YAAc,UACtB,UCjBMwE,EAA8BhG,EAAAA,WAAiB,CAAAC,EAKlDC,KAAQ,IAL2C,UACpDE,EAAS,SACTD,EACAE,GAAIC,EAAY,SACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,qBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPyF,EAAexE,YAAc,iBAC7B,UCdMyE,EAAwBjG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP0F,EAASzE,YAAc,WACvB,U,cCbA,MAAM0E,GAAgBC,EAAAA,EAAAA,GAAiB,MACjCC,EAA4BpG,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDE,EAAS,SACTD,EACAE,GAAIC,EAAY4F,KACb3F,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,kBACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP6F,EAAa5E,YAAc,eAC3B,UChBM6E,EAAwBrG,EAAAA,WAAiB,CAAAC,EAK5CC,KAAQ,IALqC,UAC9CE,EAAS,SACTD,EACAE,GAAIC,EAAY,OACbC,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,cACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGP8F,EAAS7E,YAAc,WACvB,UCbM8E,GAAgBH,EAAAA,EAAAA,GAAiB,MACjCI,EAAyBvG,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CE,EAAS,SACTD,EACAE,GAAIC,EAAYgG,KACb/F,GACJN,EAEC,OADAE,GAAWM,EAAAA,EAAAA,IAAmBN,EAAU,eACpBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,EACLE,UAAWmB,IAAWnB,EAAWD,MAC9BI,MAGPgG,EAAU/E,YAAc,YACxB,UCPM4B,EAAoBpD,EAAAA,WAAiB,CAAAC,EAWxCC,KAAQ,IAXiC,SAC1CC,EAAQ,UACRC,EAAS,GACToG,EAAE,KACFC,EAAI,OACJC,EAAM,KACNC,GAAO,EAAK,SACZ3D,EAEA3C,GAAIC,EAAY,SACbC,GACJN,EACC,MAAMuF,GAAS/E,EAAAA,EAAAA,IAAmBN,EAAU,QAC5C,OAAoBmB,EAAAA,EAAAA,KAAKhB,EAAW,CAClCJ,IAAKA,KACFK,EACHH,UAAWmB,IAAWnB,EAAWoF,EAAQgB,GAAM,MAAMA,IAAMC,GAAQ,QAAQA,IAAQC,GAAU,UAAUA,KACvG1D,SAAU2D,GAAoBrF,EAAAA,EAAAA,KAAK+D,EAAU,CAC3CrC,SAAUA,IACPA,MAGTI,EAAK5B,YAAc,OACnB,QAAeoF,OAAOC,OAAOzD,EAAM,CACjC0D,IAAKf,EACLgB,MAAOR,EACPS,SAAUZ,EACV/C,KAAMgC,EACN4B,KAAMhB,EACNiB,KAAMb,EACNc,OAAQ5B,EACR6B,OAAQ9B,EACR+B,WAAYrB,G", "sources": ["../node_modules/react-bootstrap/esm/Row.js", "pages/maker/MakerMiners.js", "../node_modules/react-bootstrap/esm/Table.js", "../node_modules/react-bootstrap/esm/Col.js", "../node_modules/react-bootstrap/esm/CardBody.js", "../node_modules/react-bootstrap/esm/CardFooter.js", "../node_modules/react-bootstrap/esm/CardHeader.js", "../node_modules/react-bootstrap/esm/CardImg.js", "../node_modules/react-bootstrap/esm/CardImgOverlay.js", "../node_modules/react-bootstrap/esm/CardLink.js", "../node_modules/react-bootstrap/esm/CardSubtitle.js", "../node_modules/react-bootstrap/esm/CardText.js", "../node_modules/react-bootstrap/esm/CardTitle.js", "../node_modules/react-bootstrap/esm/Card.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "import React, { useState, useEffect } from 'react';\r\nimport { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';\r\nimport { getSupabase } from '../../supabaseClient';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst MakerMiners = () => {\r\n    const { t } = useTranslation();\r\n    const [miners, setMiners] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    useEffect(() => {\r\n        const fetchMiners = async () => {\r\n            const supabase = getSupabase();\r\n            if (!supabase) return;\r\n\r\n            setLoading(true);\r\n            const { data: { user } } = await supabase.auth.getUser();\r\n\r\n            if (!user) {\r\n                setLoading(false);\r\n                return; // User not logged in\r\n            }\r\n\r\n            // Fetch miners associated with products from this maker\r\n            const { data, error } = await supabase\r\n                .from('miners')\r\n                .select(`\r\n                    id,\r\n                    category,\r\n                    filecoin_miner_id,\r\n                    sector_size,\r\n                    effective_until,\r\n                    created_at,\r\n                    updated_at,\r\n                    facilities (\r\n                        name\r\n                    )\r\n                `)\r\n                .order('created_at', { ascending: false });\r\n\r\n            if (error) {\r\n                console.error('Error fetching miners:', error);\r\n            } else {\r\n                setMiners(data);\r\n            }\r\n            setLoading(false);\r\n        };\r\n\r\n        fetchMiners();\r\n    }, []);\r\n\r\n    if (loading) {\r\n        return <div>{t('loading_miners')}</div>;\r\n    }\r\n\r\n    return (\r\n        <Container>\r\n                <h2 className=\"mb-4\">{t('all_miners')}</h2>\r\n                <Row>\r\n                    <Col>\r\n                        <Card>\r\n                            <Card.Body>\r\n                                <Table striped bordered hover responsive>\r\n                                    <thead>\r\n                                        <tr>\r\n                                            <th>ID</th>\r\n                                            <th>{t('category')}</th>\r\n                                            <th>{t('facility')}</th>\r\n                                            <th>{t('miner_id')}</th>\r\n                                            <th>{t('effective_until')}</th>\r\n                                            <th>{t('created_at')}</th>\r\n                                            <th>{t('updated_at')}</th>\r\n                                            <th>{t('actions')}</th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody>\r\n                                        {miners.length === 0 ? (\r\n                                            <tr>\r\n                                                <td colSpan=\"9\" className=\"text-center\">{t('no_miners_available')}</td>\r\n                                            </tr>\r\n                                        ) : (\r\n                                            miners.map(miner => (\r\n                                                <tr key={miner.id}>\r\n                                                    <td>{miner.category}</td>\r\n                                                    <td>{miner.facilities?.name || '-'}</td>\r\n                                                    <td>{miner.filecoin_miner_id}</td>\r\n                                                    <td>{miner.sector_size}</td>\r\n                                                    <td>{miner.effective_until}</td>\r\n                                                    <td>{new Date(miner.created_at).toLocaleString()}</td>\r\n                                                    <td>{new Date(miner.updated_at).toLocaleString()}</td>\r\n                                                    <td>Edit/Delete</td>\r\n                                                </tr>\r\n                                            ))\r\n                                        )}\r\n                                    </tbody>\r\n                                </Table>\r\n                            </Card.Body>\r\n                        </Card>\r\n                    </Col>\r\n                </Row>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default MakerMiners;\r\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});"], "names": ["Row", "React", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "_jsx", "classNames", "displayName", "MakerMiners", "t", "useTranslation", "miners", "setMiners", "useState", "loading", "setLoading", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "order", "ascending", "console", "fetchMiners", "children", "_jsxs", "Container", "Col", "Card", "Body", "Table", "striped", "bordered", "hover", "responsive", "length", "colSpan", "map", "miner", "_miner$facilities", "category", "facilities", "name", "filecoin_miner_id", "sector_size", "effective_until", "Date", "created_at", "toLocaleString", "updated_at", "id", "borderless", "size", "variant", "table", "responsiveClass", "colProps", "spans", "span", "offset", "useCol", "CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "CardImg", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Link", "Text", "Header", "Footer", "ImgOverlay"], "sourceRoot": ""}