"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[97],{1072:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),c=r(579);const n=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:d="div",...n}=e;const o=(0,l.oU)(r,"row"),i=(0,l.gy)(),f=(0,l.Jm)(),x=`${o}-cols`,m=[];return i.forEach(e=>{const s=n[e];let r;delete n[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==f?`-${e}`:"";null!=r&&m.push(`${x}${a}-${r}`)}),(0,c.jsx)(d,{ref:s,...n,className:t()(a,o,...m)})});n.displayName="Row";const o=n},3097:(e,s,r)=>{r.r(s),r.d(s,{default:()=>x});var a=r(5043),t=r(3519),d=r(1072),l=r(8602),c=r(8628),n=r(4196),o=r(4312),i=r(4117),f=r(579);const x=()=>{const{t:e}=(0,i.Bd)(),[s,r]=(0,a.useState)([]),[x,m]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{const e=(0,o.b)();if(!e)return;m(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void m(!1);const{data:a,error:t}=await e.from("facilities").select("\n                    id,\n                    name,\n                    created_at,\n                    updated_at\n                ").order("created_at",{ascending:!1});t?console.error("Error fetching facilities:",t):r(a),m(!1)})()},[]),x?(0,f.jsx)("div",{children:e("loading_facilities")}):(0,f.jsxs)(t.A,{children:[(0,f.jsx)("h2",{className:"mb-4",children:e("all_facilities")}),(0,f.jsx)(d.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsx)(c.A,{children:(0,f.jsx)(c.A.Body,{children:(0,f.jsxs)(n.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,f.jsx)("thead",{children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{children:"ID"}),(0,f.jsx)("th",{children:e("name")}),(0,f.jsx)("th",{children:e("created_at")}),(0,f.jsx)("th",{children:e("agent")}),(0,f.jsx)("th",{children:e("actions")})]})}),(0,f.jsx)("tbody",{children:0===s.length?(0,f.jsx)("tr",{children:(0,f.jsx)("td",{colSpan:"9",className:"text-center",children:e("no_facilities_available")})}):s.map(e=>(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:e.name}),(0,f.jsx)("td",{children:new Date(e.created_at).toLocaleString()}),(0,f.jsx)("td",{children:new Date(e.updated_at).toLocaleString()}),(0,f.jsx)("td",{children:"Edit/Delete"})]},e.id))})]})})})})})]})}},4196:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),c=r(579);const n=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:d,bordered:n,borderless:o,hover:i,size:f,variant:x,responsive:m,...h}=e;const u=(0,l.oU)(r,"table"),N=t()(a,u,x&&`${u}-${x}`,f&&`${u}-${f}`,d&&`${u}-${"string"===typeof d?`striped-${d}`:"striped"}`,n&&`${u}-bordered`,o&&`${u}-borderless`,i&&`${u}-hover`),j=(0,c.jsx)("table",{...h,className:N,ref:s});if(m){let e=`${u}-responsive`;return"string"===typeof m&&(e=`${e}-${m}`),(0,c.jsx)("div",{className:e,children:j})}return j});n.displayName="Table";const o=n},8602:(e,s,r)=>{r.d(s,{A:()=>o});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),c=r(579);const n=d.forwardRef((e,s)=>{const[{className:r,...a},{as:d="div",bsPrefix:n,spans:o}]=function(e){let{as:s,bsPrefix:r,className:a,...d}=e;r=(0,l.oU)(r,"col");const c=(0,l.gy)(),n=(0,l.Jm)(),o=[],i=[];return c.forEach(e=>{const s=d[e];let a,t,l;delete d[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:l}=s):a=s;const c=e!==n?`-${e}`:"";a&&o.push(!0===a?`${r}${c}`:`${r}${c}-${a}`),null!=l&&i.push(`order${c}-${l}`),null!=t&&i.push(`offset${c}-${t}`)}),[{...d,className:t()(a,...o,...i)},{as:s,bsPrefix:r,spans:o}]}(e);return(0,c.jsx)(d,{...a,ref:s,className:t()(r,!o.length&&n)})});n.displayName="Col";const o=n},8628:(e,s,r)=>{r.d(s,{A:()=>k});var a=r(8139),t=r.n(a),d=r(5043),l=r(7852),c=r(579);const n=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...n}=e;return a=(0,l.oU)(a,"card-body"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...n})});n.displayName="CardBody";const o=n,i=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...n}=e;return a=(0,l.oU)(a,"card-footer"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...n})});i.displayName="CardFooter";const f=i;var x=r(1778);const m=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...o}=e;const i=(0,l.oU)(r,"card-header"),f=(0,d.useMemo)(()=>({cardHeaderBsPrefix:i}),[i]);return(0,c.jsx)(x.A.Provider,{value:f,children:(0,c.jsx)(n,{ref:s,...o,className:t()(a,i)})})});m.displayName="CardHeader";const h=m,u=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:d,as:n="img",...o}=e;const i=(0,l.oU)(r,"card-img");return(0,c.jsx)(n,{ref:s,className:t()(d?`${i}-${d}`:i,a),...o})});u.displayName="CardImg";const N=u,j=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="div",...n}=e;return a=(0,l.oU)(a,"card-img-overlay"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...n})});j.displayName="CardImgOverlay";const b=j,p=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="a",...n}=e;return a=(0,l.oU)(a,"card-link"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...n})});p.displayName="CardLink";const $=p;var v=r(4488);const y=(0,v.A)("h6"),g=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=y,...n}=e;return a=(0,l.oU)(a,"card-subtitle"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...n})});g.displayName="CardSubtitle";const w=g,P=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d="p",...n}=e;return a=(0,l.oU)(a,"card-text"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...n})});P.displayName="CardText";const R=P,U=(0,v.A)("h5"),A=d.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:d=U,...n}=e;return a=(0,l.oU)(a,"card-title"),(0,c.jsx)(d,{ref:s,className:t()(r,a),...n})});A.displayName="CardTitle";const C=A,_=d.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:d,text:n,border:i,body:f=!1,children:x,as:m="div",...h}=e;const u=(0,l.oU)(r,"card");return(0,c.jsx)(m,{ref:s,...h,className:t()(a,u,d&&`bg-${d}`,n&&`text-${n}`,i&&`border-${i}`),children:f?(0,c.jsx)(o,{children:x}):x})});_.displayName="Card";const k=Object.assign(_,{Img:N,Title:C,Subtitle:w,Body:o,Link:$,Text:R,Header:h,Footer:f,ImgOverlay:b})}}]);
//# sourceMappingURL=97.2d4769dc.chunk.js.map